<template>
  <div class="cascader-example">
    <h2>AdvancedSearch Cascader级联选择器示例</h2>
    
    <div class="example-section">
      <h3>基础级联选择器</h3>
      <advanced-search
        :config="basicConfig"
        @search="handleSearch"
        @reset="handleReset"
        @field-change="handleBasicFieldChange"
      />
    </div>

    <div class="example-section">
      <h3>自定义级联选择器（Radio/Checkbox模式）</h3>
      <advanced-search
        :config="customConfig"
        @search="handleCustomSearch"
        @reset="handleCustomReset"
        @field-change="handleCustomFieldChange"
      />
    </div>

    <div class="example-section">
      <h3>高级级联选择器</h3>
      <advanced-search
        :config="advancedConfig"
        @search="handleAdvancedSearch"
        @reset="handleAdvancedReset"
        @field-change="handleAdvancedFieldChange"
      />
    </div>

    <div class="result-section" v-if="searchResult">
      <h3>搜索结果</h3>
      <pre>{{ JSON.stringify(searchResult, null, 2) }}</pre>
    </div>

    <div class="debug-section">
      <h3>实时调试信息</h3>
      <div class="debug-info">
        <h4>基础配置表单数据:</h4>
        <pre>{{ JSON.stringify(basicFormData, null, 2) }}</pre>
      </div>
      <div class="debug-info">
        <h4>自定义配置表单数据:</h4>
        <pre>{{ JSON.stringify(customFormData, null, 2) }}</pre>
      </div>
      <div class="debug-info">
        <h4>高级配置表单数据:</h4>
        <pre>{{ JSON.stringify(advancedFormData, null, 2) }}</pre>
      </div>
    </div>

    <div class="config-section">
      <h3>配置说明</h3>
      <el-collapse>
        <el-collapse-item title="基础配置" name="basic">
          <ul>
            <li><strong>地区选择</strong>: 单选模式，省市区三级联动</li>
            <li><strong>部门选择</strong>: 多选模式，支持任意级别选择</li>
            <li><strong>技能选择</strong>: 多选模式，严格父子关联</li>
          </ul>
        </el-collapse-item>
        <el-collapse-item title="高级配置" name="advanced">
          <ul>
            <li><strong>异步加载</strong>: 懒加载模式，动态获取数据</li>
            <li><strong>自定义显示</strong>: 自定义分隔符和显示方式</li>
            <li><strong>搜索过滤</strong>: 支持关键词搜索</li>
          </ul>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import AdvancedSearch from '../components/AdvancedSearch.vue';

export default {
  name: 'CascaderExample',
  components: {
    AdvancedSearch
  },
  data() {
    return {
      searchResult: null,
      basicFormData: {},
      customFormData: {},
      advancedFormData: {},
      
      // 基础配置
      basicConfig: {
        fields: [
          // 单选级联 - 地区选择
          {
            key: 'region',
            type: 'cascader',
            label: '地区',
            placeholder: '请选择地区',
            span: 8,
            multiple: false,
            showAllLevels: true,
            emitPath: false, // 只返回最后一级的值
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'guangdong',
                  label: '广东省',
                  children: [
                    {
                      value: 'guangzhou',
                      label: '广州市',
                      children: [
                        { value: 'tianhe', label: '天河区' },
                        { value: 'yuexiu', label: '越秀区' },
                        { value: 'haizhu', label: '海珠区' }
                      ]
                    },
                    {
                      value: 'shenzhen',
                      label: '深圳市',
                      children: [
                        { value: 'futian', label: '福田区' },
                        { value: 'nanshan', label: '南山区' },
                        { value: 'luohu', label: '罗湖区' }
                      ]
                    }
                  ]
                },
                {
                  value: 'beijing',
                  label: '北京市',
                  children: [
                    {
                      value: 'beijing_city',
                      label: '北京市',
                      children: [
                        { value: 'dongcheng', label: '东城区' },
                        { value: 'xicheng', label: '西城区' },
                        { value: 'chaoyang', label: '朝阳区' }
                      ]
                    }
                  ]
                }
              ]
            }
          },
          
          // 多选级联 - 部门选择
          {
            key: 'departments',
            type: 'cascader',
            label: '部门',
            placeholder: '请选择部门',
            span: 8,
            multiple: true,
            checkStrictly: false, // 父子关联
            emitPath: true, // 返回完整路径
            collapseTags: true,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'tech',
                  label: '技术部',
                  children: [
                    {
                      value: 'frontend',
                      label: '前端组',
                      children: [
                        { value: 'vue_team', label: 'Vue团队' },
                        { value: 'react_team', label: 'React团队' }
                      ]
                    },
                    {
                      value: 'backend',
                      label: '后端组',
                      children: [
                        { value: 'java_team', label: 'Java团队' },
                        { value: 'node_team', label: 'Node团队' }
                      ]
                    }
                  ]
                },
                {
                  value: 'product',
                  label: '产品部',
                  children: [
                    { value: 'pm', label: '产品经理' },
                    { value: 'ui', label: 'UI设计师' },
                    { value: 'ux', label: 'UX设计师' }
                  ]
                }
              ]
            }
          },
          
          // 多选级联 - 技能选择（严格模式）
          {
            key: 'skills',
            type: 'cascader',
            label: '技能',
            placeholder: '请选择技能',
            span: 8,
            multiple: true,
            checkStrictly: true, // 父子不关联
            emitPath: false,
            collapseTags: true,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'frontend',
                  label: '前端技能',
                  children: [
                    { value: 'vue', label: 'Vue.js' },
                    { value: 'react', label: 'React' },
                    { value: 'angular', label: 'Angular' }
                  ]
                },
                {
                  value: 'backend',
                  label: '后端技能',
                  children: [
                    { value: 'java', label: 'Java' },
                    { value: 'python', label: 'Python' },
                    { value: 'nodejs', label: 'Node.js' }
                  ]
                }
              ]
            }
          }
        ],
        buttons: {
          search: '搜索',
          reset: '重置'
        }
      },

      // 自定义级联配置（Radio/Checkbox模式）
      customConfig: {
        fields: [
          // 单选模式 - 使用Radio
          {
            key: 'singleSelect',
            type: 'cascader',
            label: '单选模式（Radio）',
            placeholder: '请选择地区',
            span: 12,
            useCustomCascader: true,  // 使用自定义级联组件
            multiple: false,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'china',
                  label: '中国',
                  children: [
                    {
                      value: 'guangdong',
                      label: '广东省',
                      children: [
                        { value: 'guangzhou', label: '广州市' },
                        { value: 'shenzhen', label: '深圳市' }
                      ]
                    },
                    {
                      value: 'beijing',
                      label: '北京市',
                      children: [
                        { value: 'dongcheng', label: '东城区' },
                        { value: 'xicheng', label: '西城区' }
                      ]
                    }
                  ]
                }
              ]
            }
          },

          // 多选模式 - 使用Checkbox
          {
            key: 'multiSelect',
            type: 'cascader',
            label: '多选模式（Checkbox）',
            placeholder: '请选择部门',
            span: 12,
            useCustomCascader: true,  // 使用自定义级联组件
            multiple: true,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'company',
                  label: '公司',
                  children: [
                    {
                      value: 'tech',
                      label: '技术部',
                      children: [
                        { value: 'frontend', label: '前端组' },
                        { value: 'backend', label: '后端组' },
                        { value: 'mobile', label: '移动端组' }
                      ]
                    },
                    {
                      value: 'product',
                      label: '产品部',
                      children: [
                        { value: 'pm', label: '产品经理' },
                        { value: 'ui', label: 'UI设计师' },
                        { value: 'ux', label: 'UX设计师' }
                      ]
                    },
                    {
                      value: 'sales',
                      label: '销售部',
                      children: [
                        { value: 'inside', label: '内销组' },
                        { value: 'outside', label: '外销组' }
                      ]
                    }
                  ]
                }
              ]
            }
          }
        ],
        buttons: {
          search: '自定义搜索',
          reset: '重置'
        }
      },

      // 高级配置
      advancedConfig: {
        fields: [
          // 异步加载级联
          {
            key: 'asyncRegion',
            type: 'cascader',
            label: '异步地区',
            placeholder: '请选择地区',
            span: 12,
            multiple: false,
            lazy: true,
            lazyLoad: this.loadAsyncData,
            cascaderProps: {
              value: 'id',
              label: 'name',
              children: 'children',
              leaf: 'leaf'
            }
          },
          
          // 可搜索级联
          {
            key: 'searchableCategory',
            type: 'cascader',
            label: '可搜索分类',
            placeholder: '请选择或搜索分类',
            span: 12,
            multiple: true,
            filterable: true,
            separator: ' > ',
            showAllLevels: true,
            dataSource: {
              type: 'function',
              function: this.loadCategories
            }
          }
        ],
        buttons: {
          search: '高级搜索',
          reset: '重置条件'
        }
      }
    };
  },
  
  methods: {
    // 基础搜索
    handleSearch(formData) {
      console.log('基础搜索:', formData);
      this.searchResult = {
        type: 'basic',
        data: formData,
        timestamp: new Date().toLocaleString()
      };
    },
    
    // 基础重置
    handleReset() {
      console.log('基础搜索重置');
      this.searchResult = null;
      this.basicFormData = {};
    },

    // 自定义搜索
    handleCustomSearch(formData) {
      console.log('自定义搜索:', formData);
      this.searchResult = {
        type: 'custom',
        data: formData,
        timestamp: new Date().toLocaleString()
      };
    },

    // 自定义重置
    handleCustomReset() {
      console.log('自定义搜索重置');
      this.searchResult = null;
      this.customFormData = {};
    },

    // 自定义字段变化处理
    handleCustomFieldChange({ field, value, type }) {
      console.log(`自定义字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.customFormData, field, value);
    },
    
    // 高级搜索
    handleAdvancedSearch(formData) {
      console.log('高级搜索:', formData);
      this.searchResult = {
        type: 'advanced',
        data: formData,
        timestamp: new Date().toLocaleString()
      };
    },
    
    // 高级重置
    handleAdvancedReset() {
      console.log('高级搜索重置');
      this.searchResult = null;
      this.advancedFormData = {};
    },

    // 基础字段变化处理
    handleBasicFieldChange({ field, value, type }) {
      console.log(`基础字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.basicFormData, field, value);
    },

    // 高级字段变化处理
    handleAdvancedFieldChange({ field, value, type }) {
      console.log(`高级字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.advancedFormData, field, value);
    },
    
    // 异步加载数据
    loadAsyncData(node, resolve) {
      const { level } = node;
      
      setTimeout(() => {
        const nodes = Array.from({ length: level + 1 }, (_, index) => ({
          id: `${node.value || 'root'}_${index}`,
          name: `选项${level}-${index}`,
          leaf: level >= 2
        }));
        
        resolve(nodes);
      }, 500);
    },
    
    // 加载分类数据
    async loadCategories() {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve([
            {
              value: 'electronics',
              label: '电子产品',
              children: [
                {
                  value: 'phones',
                  label: '手机',
                  children: [
                    { value: 'iphone', label: 'iPhone' },
                    { value: 'android', label: 'Android' }
                  ]
                },
                {
                  value: 'computers',
                  label: '电脑',
                  children: [
                    { value: 'laptop', label: '笔记本' },
                    { value: 'desktop', label: '台式机' }
                  ]
                }
              ]
            },
            {
              value: 'clothing',
              label: '服装',
              children: [
                {
                  value: 'mens',
                  label: '男装',
                  children: [
                    { value: 'shirts', label: '衬衫' },
                    { value: 'pants', label: '裤子' }
                  ]
                },
                {
                  value: 'womens',
                  label: '女装',
                  children: [
                    { value: 'dresses', label: '连衣裙' },
                    { value: 'skirts', label: '裙子' }
                  ]
                }
              ]
            }
          ]);
        }, 800);
      });
    }
  }
};
</script>

<style scoped>
.cascader-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
}

.result-section {
  margin-top: 30px;
  padding: 20px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
}

.result-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #0066cc;
}

.result-section pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.5;
}

.config-section {
  margin-top: 30px;
  padding: 20px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.config-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.config-section ul {
  margin: 0;
  padding-left: 20px;
}

.config-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.debug-section {
  margin-top: 30px;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.debug-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #856404;
}

.debug-info {
  margin-bottom: 20px;
}

.debug-info h4 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 14px;
}

.debug-info pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}
</style>
