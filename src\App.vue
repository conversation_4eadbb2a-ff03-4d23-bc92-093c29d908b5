<template>
  <div id="app">
    <div class="container">
      <h1>高级查找组件示例</h1>

      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="原始示例" name="original">
          <advanced-search
            ref="advancedSearch"
            :config="searchConfig"
            @search="handleSearch"
            @reset="handleReset"
            @extra-button="handleExtraButton"
            @field-change="handleFieldChange"
          />

          <div class="result-section" v-if="searchResult">
            <h3>搜索结果</h3>
            <pre>{{ JSON.stringify(searchResult, null, 2) }}</pre>
          </div>
        </el-tab-pane>

        <el-tab-pane label="完整功能示例" name="complete">
          <complete-example />
        </el-tab-pane>

        <el-tab-pane label="异步数据测试" name="async-test">
          <async-test />
        </el-tab-pane>

        <el-tab-pane label="日期约束示例" name="date-constraint">
          <date-constraint-example />
        </el-tab-pane>

        <el-tab-pane label="高级表单组件" name="advanced-form">
          <advanced-form-example />
        </el-tab-pane>

        <el-tab-pane label="高级表格组件" name="advanced-table">
          <advanced-table-example />
        </el-tab-pane>

        <el-tab-pane label="Select默认值示例" name="select-default">
          <select-default-value-example />
        </el-tab-pane>

        <el-tab-pane label="现代化主题" name="themes">
          <advanced-search-theme-example />
        </el-tab-pane>

        <el-tab-pane label="Cascader级联选择" name="cascader">
          <cascader-example />
        </el-tab-pane>

        <el-tab-pane label="Cascader多选测试" name="cascader-test">
          <cascader-multiple-test />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import AdvancedSearch from './components/AdvancedSearch.vue';
import CompleteExample from './examples/CompleteExample.vue';
import AsyncTest from './test/AsyncTest.vue';
import DateConstraintExample from './examples/DateConstraintExample.vue';
import AdvancedFormExample from './examples/AdvancedFormExample.vue';
import AdvancedTableExample from './examples/AdvancedTableExample.vue';
import SelectDefaultValueExample from './examples/SelectDefaultValueExample.vue';
import AdvancedSearchThemeExample from './examples/AdvancedSearchThemeExample.vue';
import CascaderExample from './examples/CascaderExample.vue';
import CascaderMultipleTest from './test/CascaderMultipleTest.vue';

export default {
  name: 'App',
  components: {
    AdvancedSearch,
    CompleteExample,
    AsyncTest,
    DateConstraintExample,
    AdvancedFormExample,
    AdvancedTableExample,
    SelectDefaultValueExample,
    AdvancedSearchThemeExample,
    CascaderExample,
    CascaderMultipleTest
  },
  data() {
    return {
      activeTab: 'original',
      searchResult: null,
      searchConfig: {
        fields: [
          // Select类型示例 - 省市区三级联动
          {
            key: 'province',
            type: 'select',
            label: '省份',
            placeholder: '请选择省份',
            span: 8,
            dataSource: {
              type: 'static',
              data: [
                { value: 'beijing', label: '北京市' },
                { value: 'shanghai', label: '上海市' },
                { value: 'guangdong', label: '广东省' }
              ]
            },
            children: ['city'],
            rules: [
              { required: true, message: '请选择省份', trigger: 'change' }
            ]
          },
          {
            key: 'city',
            type: 'select',
            label: '城市',
            placeholder: '请选择城市',
            span: 8,
            dataSource: {
              type: 'related',
              parentKey: 'province',
              data: {
                beijing: [
                  { value: 'dongcheng', label: '东城区' },
                  { value: 'xicheng', label: '西城区' }
                ],
                shanghai: [
                  { value: 'huangpu', label: '黄浦区' },
                  { value: 'xuhui', label: '徐汇区' }
                ],
                guangdong: [
                  { value: 'guangzhou', label: '广州市' },
                  { value: 'shenzhen', label: '深圳市' }
                ]
              }
            },
            children: ['district']
          },
          {
            key: 'district',
            type: 'select',
            label: '区县',
            placeholder: '请选择区县',
            span: 8,
            dataSource: {
              type: 'related',
              parentKey: 'city',
              data: {
                dongcheng: [
                  { value: 'wangfujing', label: '王府井街道' }
                ],
                xicheng: [
                  { value: 'xinjiekou', label: '新街口街道' }
                ],
                huangpu: [
                  { value: 'nanjing', label: '南京东路街道' }
                ],
                xuhui: [
                  { value: 'tianlin', label: '田林街道' }
                ],
                guangzhou: [
                  { value: 'tianhe', label: '天河区' }
                ],
                shenzhen: [
                  { value: 'futian', label: '福田区' }
                ]
              }
            }
          },
          
          // 状态选择 - 多选
          {
            key: 'status',
            type: 'select',
            label: '状态',
            placeholder: '请选择状态',
            span: 8,
            multiple: true,
            dataSource: {
              type: 'static',
              data: [
                { value: 'active', label: '激活' },
                { value: 'inactive', label: '未激活' },
                { value: 'pending', label: '待审核' },
                { value: 'rejected', label: '已拒绝' }
              ]
            }
          },
          
          // Date类型示例
          {
            key: 'startDate',
            type: 'date',
            label: '开始日期',
            placeholder: '选择开始日期',
            span: 8,
            dateType: 'date',
            rules: [
              { required: true, message: '请选择开始日期', trigger: 'change' }
            ]
          },
          {
            key: 'endDate',
            type: 'date',
            label: '结束日期',
            placeholder: '选择结束日期',
            span: 8,
            dateType: 'date',
            constraints: {
              type: 'after',
              relatedField: 'startDate',
              includeToday: true  // 包含开始日期当天
            }
          },
          
          // 日期范围
          {
            key: 'dateRange',
            type: 'date',
            label: '日期范围',
            placeholder: '选择日期范围',
            span: 8,
            dateType: 'daterange'
          },
          
          // 限制日期选择范围
          {
            key: 'limitedDate',
            type: 'date',
            label: '限制日期',
            placeholder: '只能在范围内选择',
            span: 8,
            dateType: 'daterange',
            constraints: {
              type: 'range',
              relatedField: 'dateRange'
            }
          },
          
          // Input类型示例
          {
            key: 'keyword',
            type: 'input',
            label: '关键词',
            placeholder: '请输入关键词',
            span: 8,
            maxlength: 50,
            showWordLimit: true,
            rules: [
              { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
            ]
          },
          {
            key: 'email',
            type: 'input',
            label: '邮箱',
            placeholder: '请输入邮箱地址',
            span: 8,
            rules: [
              { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ]
          },
          {
            key: 'phone',
            type: 'input',
            label: '手机号',
            placeholder: '请输入手机号',
            span: 8,
            rules: [
              { 
                validator: (value, callback) => {
                  if (!value) {
                    callback();
                    return;
                  }
                  const phoneReg = /^1[3-9]\d{9}$/;
                  if (!phoneReg.test(value)) {
                    callback(new Error('请输入正确的手机号'));
                  } else {
                    callback();
                  }
                }, 
                trigger: 'blur' 
              }
            ]
          },
          
          // 数字输入
          {
            key: 'age',
            type: 'input',
            label: '年龄',
            placeholder: '请输入年龄',
            span: 8,
            inputType: 'number',
            rules: [
              { 
                validator: (value, callback) => {
                  if (!value) {
                    callback();
                    return;
                  }
                  const age = parseInt(value);
                  if (isNaN(age) || age < 0 || age > 150) {
                    callback(new Error('请输入有效的年龄(0-150)'));
                  } else {
                    callback();
                  }
                }, 
                trigger: 'blur' 
              }
            ]
          }
        ],
        buttons: {
          search: '搜索',
          reset: '重置',
          extra: [
            {
              key: 'export',
              label: '导出',
              type: 'success',
              handler: (formData) => {
                console.log('导出数据:', formData);
              }
            },
            {
              key: 'save',
              label: '保存查询',
              type: 'warning'
            }
          ]
        }
      }
    };
  },
  methods: {
    handleSearch(formData) {
      console.log('搜索参数:', formData);
      this.searchResult = formData;
      this.$message.success('搜索完成');
    },
    
    handleReset() {
      console.log('重置搜索');
      this.searchResult = null;
      this.$message.info('已重置搜索条件');
    },
    
    handleExtraButton({ button, formData }) {
      console.log('额外按钮点击:', button.key, formData);
      
      if (button.key === 'save') {
        this.$message.success('查询条件已保存');
      }
    },
    
    handleFieldChange({ field, value, type }) {
      console.log('字段变化:', field, value, type);
      
      // 处理省市区联动
      if (field === 'province') {
        this.updateCityOptions(value);
      } else if (field === 'city') {
        this.updateDistrictOptions(value);
      }
    },
    
    updateCityOptions(province) {
      // 这里可以根据省份动态更新城市选项
      console.log('更新城市选项:', province);
    },
    
    updateDistrictOptions(city) {
      // 这里可以根据城市动态更新区县选项
      console.log('更新区县选项:', city);
    }
  }
};
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #409EFF;
  margin-bottom: 30px;
}

.result-section {
  margin-top: 30px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 4px;
}

.result-section h3 {
  margin-top: 0;
  color: #333;
}

.result-section pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
}
</style>
