# AdvancedSearch组件Cascader级联选择器配置指南

## 概述

AdvancedSearch组件现在完全支持Element UI的Cascader级联选择器，提供了丰富的配置选项，支持单选、多选、异步加载、懒加载等多种模式。

## 基础配置

### 1. 字段类型

```javascript
{
  key: 'region',
  type: 'cascader',  // 指定为cascader类型
  label: '地区',
  placeholder: '请选择地区',
  span: 8
}
```

### 2. 数据源配置

#### 静态数据源
```javascript
{
  key: 'region',
  type: 'cascader',
  label: '地区',
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'guangdong',
        label: '广东省',
        children: [
          {
            value: 'guangzhou',
            label: '广州市',
            children: [
              { value: 'tianhe', label: '天河区' },
              { value: 'yuexiu', label: '越秀区' }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 异步数据源
```javascript
{
  key: 'category',
  type: 'cascader',
  label: '分类',
  dataSource: {
    type: 'function',
    function: async () => {
      const response = await fetch('/api/categories');
      return response.json();
    }
  }
}
```

## 单选配置

### 基础单选
```javascript
{
  key: 'region',
  type: 'cascader',
  label: '地区',
  multiple: false,  // 单选模式（默认）
  emitPath: false,  // 只返回最后一级的值
  showAllLevels: true,  // 显示完整路径
  dataSource: {
    type: 'static',
    data: [
      // 数据结构...
    ]
  }
}
```

### 单选高级配置
```javascript
{
  key: 'department',
  type: 'cascader',
  label: '部门',
  multiple: false,
  emitPath: true,  // 返回完整路径数组
  checkStrictly: true,  // 可以选择任意级别
  showAllLevels: true,
  separator: ' > ',  // 自定义分隔符
  filterable: true,  // 支持搜索
  clearable: true,  // 支持清空
  dataSource: {
    // 数据源配置...
  }
}
```

## 多选配置

### 基础多选
```javascript
{
  key: 'skills',
  type: 'cascader',
  label: '技能',
  multiple: true,  // 多选模式
  emitPath: false,  // 只返回值，不返回路径
  collapseTags: true,  // 折叠标签显示
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'frontend',
        label: '前端技能',
        children: [
          { value: 'vue', label: 'Vue.js' },
          { value: 'react', label: 'React' }
        ]
      }
    ]
  }
}
```

### 多选高级配置
```javascript
{
  key: 'permissions',
  type: 'cascader',
  label: '权限',
  multiple: true,
  checkStrictly: false,  // 父子关联选择
  emitPath: true,  // 返回完整路径
  collapseTags: true,
  showAllLevels: true,
  filterable: true,
  dataSource: {
    // 数据源配置...
  }
}
```

## 懒加载配置

### 基础懒加载
```javascript
{
  key: 'asyncRegion',
  type: 'cascader',
  label: '异步地区',
  lazy: true,  // 启用懒加载
  lazyLoad: (node, resolve) => {
    // 异步加载逻辑
    setTimeout(() => {
      const nodes = [
        { value: 'option1', label: '选项1', leaf: true },
        { value: 'option2', label: '选项2', leaf: false }
      ];
      resolve(nodes);
    }, 500);
  },
  cascaderProps: {
    value: 'value',
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    leaf: 'leaf'
  }
}
```

### 自定义属性映射
```javascript
{
  key: 'customData',
  type: 'cascader',
  label: '自定义数据',
  cascaderProps: {
    value: 'id',      // 值字段
    label: 'name',    // 标签字段
    children: 'items', // 子节点字段
    disabled: 'isDisabled',  // 禁用字段
    leaf: 'isLeaf'    // 叶子节点字段
  },
  dataSource: {
    type: 'static',
    data: [
      {
        id: '1',
        name: '选项1',
        items: [
          { id: '1-1', name: '子选项1', isLeaf: true }
        ]
      }
    ]
  }
}
```

## 完整配置示例

### 地区三级联动（单选）
```javascript
{
  key: 'region',
  type: 'cascader',
  label: '地区',
  placeholder: '请选择省市区',
  span: 8,
  multiple: false,
  emitPath: false,  // 只返回区县代码
  showAllLevels: true,
  separator: ' / ',
  clearable: true,
  filterable: true,
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'guangdong',
        label: '广东省',
        children: [
          {
            value: 'guangzhou',
            label: '广州市',
            children: [
              { value: 'tianhe', label: '天河区' },
              { value: 'yuexiu', label: '越秀区' },
              { value: 'haizhu', label: '海珠区' }
            ]
          },
          {
            value: 'shenzhen',
            label: '深圳市',
            children: [
              { value: 'futian', label: '福田区' },
              { value: 'nanshan', label: '南山区' }
            ]
          }
        ]
      }
    ]
  }
}
```

### 部门权限选择（多选）
```javascript
{
  key: 'departments',
  type: 'cascader',
  label: '部门权限',
  placeholder: '请选择部门',
  span: 12,
  multiple: true,
  checkStrictly: false,  // 父子关联
  emitPath: true,  // 返回完整路径
  collapseTags: true,
  showAllLevels: true,
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'tech',
        label: '技术部',
        children: [
          {
            value: 'frontend',
            label: '前端组',
            children: [
              { value: 'vue_team', label: 'Vue团队' },
              { value: 'react_team', label: 'React团队' }
            ]
          },
          {
            value: 'backend',
            label: '后端组',
            children: [
              { value: 'java_team', label: 'Java团队' },
              { value: 'node_team', label: 'Node团队' }
            ]
          }
        ]
      }
    ]
  }
}
```

### 异步加载分类（懒加载）
```javascript
{
  key: 'asyncCategory',
  type: 'cascader',
  label: '异步分类',
  placeholder: '请选择分类',
  span: 8,
  lazy: true,
  lazyLoad: async (node, resolve) => {
    try {
      const response = await fetch(`/api/categories?parentId=${node.value || 0}`);
      const data = await response.json();
      resolve(data);
    } catch (error) {
      console.error('加载失败:', error);
      resolve([]);
    }
  },
  cascaderProps: {
    value: 'id',
    label: 'name',
    leaf: 'isLeaf'
  }
}
```

## 配置属性详解

### 基础属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `multiple` | Boolean | false | 是否多选 |
| `emitPath` | Boolean | true | 是否返回完整路径 |
| `checkStrictly` | Boolean | false | 是否严格的遵守父子节点不互相关联 |
| `showAllLevels` | Boolean | true | 是否显示完整路径 |
| `collapseTags` | Boolean | false | 多选时是否折叠Tag |
| `separator` | String | ' / ' | 选项分隔符 |
| `filterable` | Boolean | false | 是否可搜索 |
| `clearable` | Boolean | true | 是否支持清空选项 |

### 懒加载属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `lazy` | Boolean | false | 是否动态加载子节点 |
| `lazyLoad` | Function | - | 加载动态数据的方法 |

### 自定义属性映射
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `cascaderProps.value` | String | 'value' | 指定选项的值字段 |
| `cascaderProps.label` | String | 'label' | 指定选项的标签字段 |
| `cascaderProps.children` | String | 'children' | 指定选项的子选项字段 |
| `cascaderProps.disabled` | String | 'disabled' | 指定选项的禁用字段 |
| `cascaderProps.leaf` | String | 'leaf' | 指定选项的叶子节点字段 |

## 事件处理

### 值变化事件
```javascript
// 在AdvancedSearch组件中监听
@field-change="handleFieldChange"

// 处理方法
handleFieldChange({ field, value, type }) {
  if (type === 'cascader') {
    console.log(`Cascader字段 ${field} 值变化:`, value);
  }
}
```

## 最佳实践

### 1. 数据结构设计
- 保持数据结构的一致性
- 合理设置叶子节点标识
- 提供清晰的层级关系

### 2. 性能优化
- 大数据量时使用懒加载
- 合理设置缓存策略
- 避免过深的层级结构

### 3. 用户体验
- 提供搜索功能
- 合理的默认值设置
- 清晰的占位符文本

### 4. 错误处理
- 异步加载的错误处理
- 数据格式验证
- 用户友好的错误提示

## 注意事项

1. **数据格式**: 确保数据结构符合Element UI Cascader的要求
2. **性能考虑**: 大量数据时建议使用懒加载
3. **兼容性**: 确保与其他字段类型的兼容性
4. **验证规则**: 可以配合表单验证使用

通过以上配置，您可以在AdvancedSearch组件中灵活使用Cascader级联选择器，满足各种复杂的业务需求。
