<template>
  <div class="advanced-search">
    <el-form 
      ref="searchForm" 
      :model="formData" 
      :rules="formRules" 
      label-width="120px"
      class="search-form"
    >
      <el-row :gutter="20">
        <el-col 
          v-for="field in config.fields" 
          :key="field.key"
          :span="field.span || 8"
        >
          <!-- Select类型 -->
          <el-form-item 
            v-if="field.type === 'select'" 
            :label="field.label"
            :prop="field.key"
          >
            <el-select 
              v-model="formData[field.key]"
              :placeholder="field.placeholder || '请选择'"
              :clearable="field.clearable !== false"
              :multiple="field.multiple"
              @change="handleSelectChange(field, $event)"
              style="width: 100%"
            >
              <el-option
                v-for="option in getSelectOptions(field)"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <!-- Date类型 -->
          <el-form-item 
            v-if="field.type === 'date'" 
            :label="field.label"
            :prop="field.key"
          >
            <el-date-picker
              v-model="formData[field.key]"
              :type="field.dateType || 'date'"
              :placeholder="field.placeholder || '选择日期'"
              :picker-options="getDatePickerOptions(field)"
              :clearable="field.clearable !== false"
              style="width: 100%"
              @change="handleDateChange(field, $event)"
            />
          </el-form-item>

          <!-- Cascader类型 -->
          <el-form-item
            v-if="field.type === 'cascader'"
            :label="field.label"
            :prop="field.key"
          >
            <el-cascader
              v-model="formData[field.key]"
              :options="getCascaderOptions(field)"
              :props="getCascaderProps(field)"
              :placeholder="field.placeholder || '请选择'"
              :clearable="field.clearable !== false"
              :filterable="field.filterable"
              :show-all-levels="field.showAllLevels !== false"
              :collapse-tags="field.collapseTags"
              :separator="field.separator || ' / '"
              style="width: 100%"
              @change="handleCascaderChange(field, $event)"
            />
          </el-form-item>

          <!-- Input类型 -->
          <el-form-item 
            v-if="field.type === 'input'" 
            :label="field.label"
            :prop="field.key"
          >
            <el-input
              v-model="formData[field.key]"
              :placeholder="field.placeholder || '请输入'"
              :clearable="field.clearable !== false"
              :type="field.inputType || 'text'"
              :maxlength="field.maxlength"
              :show-word-limit="field.showWordLimit"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 按钮组 -->
      <el-row>
        <el-col :span="24" class="button-group">
          <el-button 
            type="primary" 
            @click="handleSearch"
            :loading="searchLoading"
          >
            {{ config.buttons.search || '搜索' }}
          </el-button>
          <el-button @click="handleReset">
            {{ config.buttons.reset || '重置' }}
          </el-button>
          <el-button
            v-for="btn in config.buttons.extra"
            :key="btn.key"
            :type="btn.type || 'default'"
            @click="handleExtraButton(btn)"
          >
            {{ btn.label }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AdvancedSearch',
  props: {
    config: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      formData: {},
      formRules: {},
      searchLoading: false,
      selectOptionsCache: {},
      cascaderOptionsCache: {},
      dateConstraints: {}
    };
  },
  created() {
    this.initializeForm();
  },
  methods: {
    // 初始化表单
    initializeForm() {
      const formData = {};
      const formRules = {};

      this.config.fields.forEach(field => {
        // 初始化表单数据
        formData[field.key] = this.getFieldDefaultValue(field);

        // 初始化验证规则
        if (field.rules) {
          formRules[field.key] = this.buildValidationRules(field);
        }

        // 初始化select选项缓存
        if (field.type === 'select' && field.dataSource) {
          this.loadSelectOptions(field).then(() => {
            // 选项加载完成后，重新设置默认值
            this.setFieldDefaultValueFromOptions(field);
          });
        }

        // 初始化cascader选项缓存
        if (field.type === 'cascader' && field.dataSource) {
          this.loadCascaderOptions(field);
        }
      });

      this.formData = formData;
      this.formRules = formRules;
    },

    // 获取字段默认值
    getFieldDefaultValue(field) {
      // 如果有明确的defaultValue配置，优先使用
      if (field.defaultValue !== undefined) {
        return field.defaultValue;
      }

      // 如果配置了从选项中获取默认值（仅select类型）
      if (field.type === 'select' && field.defaultFromOptions) {
        return this.getDefaultValueFromOptions(field);
      }

      // 如果是date类型且配置了默认日期
      if (field.type === 'date' && field.defaultDate) {
        return this.getDateDefaultValue(field);
      }

      // 默认值
      if (field.type === 'select') {
        return field.multiple ? [] : '';
      } else if (field.type === 'cascader') {
        return field.multiple ? [] : [];
      } else if (field.type === 'date') {
        const dateType = field.dateType || 'date';
        return dateType.includes('range') ? [] : '';
      } else {
        return field.multiple ? [] : '';
      }
    },

    // 从选项中获取默认值
    getDefaultValueFromOptions(field) {
      const options = this.getSelectOptions(field);
      if (!options || options.length === 0) {
        return field.multiple ? [] : '';
      }

      const config = field.defaultFromOptions;

      // 如果是字符串配置，表示获取方式
      if (typeof config === 'string') {
        switch (config) {
          case 'first':
            return field.multiple ? [options[0].value] : options[0].value;
          case 'last':
            return field.multiple ? [options[options.length - 1].value] : options[options.length - 1].value;
          case 'all':
            return field.multiple ? options.map(opt => opt.value) : options[0].value;
          default:
            return field.multiple ? [] : '';
        }
      }

      // 如果是对象配置
      if (typeof config === 'object') {
        // 根据索引获取
        if (config.index !== undefined) {
          const index = config.index;
          if (index >= 0 && index < options.length) {
            return field.multiple ? [options[index].value] : options[index].value;
          }
        }

        // 根据条件筛选
        if (config.filter) {
          const filteredOptions = options.filter(config.filter);
          if (filteredOptions.length > 0) {
            if (field.multiple) {
              return config.multiple === 'all'
                ? filteredOptions.map(opt => opt.value)
                : [filteredOptions[0].value];
            } else {
              return filteredOptions[0].value;
            }
          }
        }

        // 根据值匹配
        if (config.value !== undefined) {
          const matchedOption = options.find(opt => opt.value === config.value);
          if (matchedOption) {
            return field.multiple ? [matchedOption.value] : matchedOption.value;
          }
        }

        // 根据标签匹配
        if (config.label !== undefined) {
          const matchedOption = options.find(opt => opt.label === config.label);
          if (matchedOption) {
            return field.multiple ? [matchedOption.value] : matchedOption.value;
          }
        }
      }

      return field.multiple ? [] : '';
    },

    // 获取日期默认值
    getDateDefaultValue(field) {
      const config = field.defaultDate;
      const dateType = field.dateType || 'date';
      const isRange = dateType.includes('range');

      // 如果是字符串配置
      if (typeof config === 'string') {
        switch (config) {
          case 'today':
            return isRange ? [new Date(), new Date()] : new Date();
          case 'yesterday':
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            return isRange ? [yesterday, yesterday] : yesterday;
          case 'tomorrow':
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return isRange ? [tomorrow, tomorrow] : tomorrow;
          case 'thisWeek':
            if (isRange) {
              const today = new Date();
              const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
              const lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));
              return [firstDay, lastDay];
            }
            return new Date();
          case 'thisMonth':
            if (isRange) {
              const today = new Date();
              const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
              const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
              return [firstDay, lastDay];
            }
            return new Date();
          case 'lastWeek':
            if (isRange) {
              const today = new Date();
              const lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
              const lastWeekEnd = new Date(today.setDate(today.getDate() - today.getDay() - 1));
              return [lastWeekStart, lastWeekEnd];
            }
            return new Date();
          case 'lastMonth':
            if (isRange) {
              const today = new Date();
              const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
              const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
              return [firstDay, lastDay];
            }
            return new Date();
          default:
            return isRange ? [] : '';
        }
      }

      // 如果是对象配置
      if (typeof config === 'object') {
        // 相对日期配置
        if (config.relative) {
          const baseDate = new Date();
          const { days = 0, months = 0, years = 0 } = config.relative;

          baseDate.setDate(baseDate.getDate() + days);
          baseDate.setMonth(baseDate.getMonth() + months);
          baseDate.setFullYear(baseDate.getFullYear() + years);

          if (isRange && config.range) {
            const endDate = new Date(baseDate);
            const { days: rangeDays = 0, months: rangeMonths = 0, years: rangeYears = 0 } = config.range;

            endDate.setDate(endDate.getDate() + rangeDays);
            endDate.setMonth(endDate.getMonth() + rangeMonths);
            endDate.setFullYear(endDate.getFullYear() + rangeYears);

            return [baseDate, endDate];
          }

          return isRange ? [baseDate, baseDate] : baseDate;
        }

        // 固定日期配置
        if (config.date) {
          const date = new Date(config.date);
          if (isRange && config.endDate) {
            return [date, new Date(config.endDate)];
          }
          return isRange ? [date, date] : date;
        }

        // 范围配置
        if (isRange && config.startDate && config.endDate) {
          return [new Date(config.startDate), new Date(config.endDate)];
        }
      }

      return isRange ? [] : '';
    },

    // 选项加载完成后设置默认值
    setFieldDefaultValueFromOptions(field) {
      if (field.defaultFromOptions && !field.defaultValue) {
        const defaultValue = this.getDefaultValueFromOptions(field);
        if (defaultValue !== (field.multiple ? [] : '')) {
          this.$set(this.formData, field.key, defaultValue);
        }
      }
    },

    // 设置子字段默认值
    setChildFieldDefaultValue(childField, options) {
      // 只有当子字段当前没有值且配置了defaultFromOptions时才设置默认值
      if (childField.defaultFromOptions &&
          (!this.formData[childField.key] ||
           (childField.multiple && Array.isArray(this.formData[childField.key]) && this.formData[childField.key].length === 0) ||
           (!childField.multiple && !this.formData[childField.key]))) {

        const defaultValue = this.getDefaultValueFromOptionsWithData(childField, options);
        if (defaultValue !== (childField.multiple ? [] : '')) {
          this.$set(this.formData, childField.key, defaultValue);
          console.log(`🎯 设置子字段 ${childField.key} 默认值:`, defaultValue);
        }
      }
    },

    // 从指定的选项数据中获取默认值
    getDefaultValueFromOptionsWithData(field, options) {
      if (!options || options.length === 0) {
        return field.multiple ? [] : '';
      }

      const config = field.defaultFromOptions;

      // 如果是字符串配置，表示获取方式
      if (typeof config === 'string') {
        switch (config) {
          case 'first':
            return field.multiple ? [options[0].value] : options[0].value;
          case 'last':
            return field.multiple ? [options[options.length - 1].value] : options[options.length - 1].value;
          case 'all':
            return field.multiple ? options.map(opt => opt.value) : options[0].value;
          default:
            return field.multiple ? [] : '';
        }
      }

      // 如果是对象配置
      if (typeof config === 'object') {
        // 根据索引获取
        if (config.index !== undefined) {
          const index = config.index;
          if (index >= 0 && index < options.length) {
            return field.multiple ? [options[index].value] : options[index].value;
          }
        }

        // 根据条件筛选
        if (config.filter) {
          const filteredOptions = options.filter(config.filter);
          if (filteredOptions.length > 0) {
            if (field.multiple) {
              return config.multiple === 'all'
                ? filteredOptions.map(opt => opt.value)
                : [filteredOptions[0].value];
            } else {
              return filteredOptions[0].value;
            }
          }
        }

        // 根据值匹配
        if (config.value !== undefined) {
          const matchedOption = options.find(opt => opt.value === config.value);
          if (matchedOption) {
            return field.multiple ? [matchedOption.value] : matchedOption.value;
          }
        }

        // 根据标签匹配
        if (config.label !== undefined) {
          const matchedOption = options.find(opt => opt.label === config.label);
          if (matchedOption) {
            return field.multiple ? [matchedOption.value] : matchedOption.value;
          }
        }
      }

      return field.multiple ? [] : '';
    },

    // 构建验证规则
    buildValidationRules(field) {
      const rules = [];

      field.rules.forEach(rule => {
        const ruleObj = { ...rule };

        // 自定义验证器
        if (rule.validator && typeof rule.validator === 'function') {
          ruleObj.validator = (rule, value, callback) => {
            rule.validator(value, callback, this.formData);
          };
        }

        rules.push(ruleObj);
      });

      return rules;
    },

    // 获取select选项
    getSelectOptions(field) {
      if (!field.dataSource) return [];

      // 静态数据源
      if (field.dataSource.type === 'static') {
        return field.dataSource.data || [];
      }

      // 函数数据源
      if (field.dataSource.type === 'function') {
        // 如果有parentKey，说明是关联数据源
        if (field.dataSource.parentKey) {
          return this.getRelatedOptions(field);
        }
        // 普通函数数据源
        return this.selectOptionsCache[field.key] || [];
      }

      // 关联数据源（三级联动）
      if (field.dataSource.type === 'related') {
        return this.getRelatedOptions(field);
      }

      return [];
    },

    // 加载select选项
    async loadSelectOptions(field) {
      if (field.dataSource.type === 'function' && field.dataSource.function) {
        // 如果有parentKey，说明是关联数据源，不在初始化时加载
        if (field.dataSource.parentKey) {
          return;
        }

        try {
          const options = await field.dataSource.function(this.formData);
          this.$set(this.selectOptionsCache, field.key, options);

          // 选项加载完成后，设置从选项中获取的默认值
          this.setFieldDefaultValueFromOptions(field);
        } catch (error) {
          console.error('加载选项失败:', error);
          this.$message.error('加载选项失败');
        }
      }
    },

    // 获取关联选项（三级联动）
    getRelatedOptions(field) {
      const { parentKey, data, type } = field.dataSource;
      const parentValue = this.formData[parentKey];

      if (!parentValue) return [];

      // 静态关联数据源
      if (type === 'related' && data && data[parentValue]) {
        return data[parentValue];
      }

      // 异步关联数据源 - 从缓存中获取
      if (type === 'function') {
        return this.selectOptionsCache[`${field.key}_${parentValue}`] || [];
      }

      return [];
    },

    // 处理select变化
    async handleSelectChange(field, value) {
      // 清空子级选项
      if (field.children) {
        field.children.forEach(childKey => {
          this.formData[childKey] = field.multiple ? [] : '';
        });
      }

      // 加载子级选项
      if (value) {
        await this.loadRelatedOptions(field, value);
      }

      this.$emit('field-change', { field: field.key, value, type: 'select' });
    },

    // 获取cascader选项
    getCascaderOptions(field) {
      if (!field.dataSource) return [];

      // 静态数据源
      if (field.dataSource.type === 'static') {
        return field.dataSource.data || [];
      }

      // 函数数据源
      if (field.dataSource.type === 'function') {
        return this.cascaderOptionsCache[field.key] || [];
      }

      // 关联数据源
      if (field.dataSource.type === 'related') {
        return this.getCascaderRelatedOptions(field);
      }

      return [];
    },

    // 获取cascader配置属性
    getCascaderProps(field) {
      const defaultProps = {
        value: 'value',
        label: 'label',
        children: 'children',
        disabled: 'disabled',
        leaf: 'leaf'
      };

      // 合并用户自定义配置
      const userProps = field.cascaderProps || {};
      const props = { ...defaultProps, ...userProps };

      // 设置单选/多选
      if (field.multiple) {
        props.multiple = true;
        // 多选时的其他配置
        if (field.checkStrictly !== undefined) {
          props.checkStrictly = field.checkStrictly;
        }
        if (field.emitPath !== undefined) {
          props.emitPath = field.emitPath;
        }
      } else {
        props.multiple = false;
        // 单选时的配置
        if (field.checkStrictly !== undefined) {
          props.checkStrictly = field.checkStrictly;
        }
        if (field.emitPath !== undefined) {
          props.emitPath = field.emitPath;
        } else {
          // 单选默认只返回最后一级的值
          props.emitPath = false;
        }
      }

      // 懒加载配置
      if (field.lazy) {
        props.lazy = true;
        props.lazyLoad = field.lazyLoad || this.defaultLazyLoad;
      }

      return props;
    },

    // 处理cascader变化
    handleCascaderChange(field, value) {
      this.$emit('field-change', { field: field.key, value, type: 'cascader' });
    },

    // 获取cascader关联选项
    getCascaderRelatedOptions(field) {
      const parentKey = field.dataSource.parentKey;
      const parentValue = this.formData[parentKey];

      if (!parentValue) return [];

      const cacheKey = `${field.key}_${parentValue}`;
      return this.cascaderOptionsCache[cacheKey] || [];
    },

    // 默认懒加载函数
    defaultLazyLoad(node, resolve) {
      // 用户需要在字段配置中提供具体的懒加载实现
      console.warn('请在字段配置中提供lazyLoad函数');
      resolve([]);
    },

    // 加载cascader选项
    async loadCascaderOptions(field) {
      if (field.dataSource.type === 'function' && field.dataSource.function) {
        try {
          const options = await field.dataSource.function(this.formData);
          this.$set(this.cascaderOptionsCache, field.key, options);
        } catch (error) {
          console.error('加载cascader选项失败:', error);
          this.$message.error('加载选项失败');
        }
      }
    },

    // 加载关联选项
    async loadRelatedOptions(field, parentValue) {
      const childFields = this.config.fields.filter(f =>
        f.dataSource && f.dataSource.parentKey === field.key
      );

      for (const childField of childFields) {
        if (childField.dataSource.type === 'function' && childField.dataSource.function) {
          try {
            const options = await childField.dataSource.function(parentValue, this.formData);
            this.$set(this.selectOptionsCache, `${childField.key}_${parentValue}`, options);

            // 选项加载完成后，设置子字段的默认值
            this.setChildFieldDefaultValue(childField, options);
          } catch (error) {
            console.error('加载关联选项失败:', error);
            this.$message.error('加载选项失败');
          }
        }
      }
    },

    // 获取日期选择器选项
    getDatePickerOptions(field) {
      const options = {};

      // 日期约束
      if (field.constraints) {
        const { type, relatedField, includeToday = false } = field.constraints;

        if (type === 'after' && relatedField) {
          const relatedDate = this.formData[relatedField];
          if (relatedDate) {
            options.disabledDate = (time) => {
              const relatedTime = new Date(relatedDate).getTime();
              const currentTime = time.getTime();

              if (includeToday) {
                // 包含当天：禁用小于关联日期的日期
                return currentTime < relatedTime;
              } else {
                // 不包含当天：禁用小于等于关联日期的日期
                return currentTime <= relatedTime;
              }
            };
          }
        }

        if (type === 'before' && relatedField) {
          const relatedDate = this.formData[relatedField];
          if (relatedDate) {
            options.disabledDate = (time) => {
              const relatedTime = new Date(relatedDate).getTime();
              const currentTime = time.getTime();

              if (includeToday) {
                // 包含当天：禁用大于关联日期的日期
                return currentTime > relatedTime;
              } else {
                // 不包含当天：禁用大于等于关联日期的日期
                return currentTime >= relatedTime;
              }
            };
          }
        }

        if (type === 'range' && relatedField) {
          const relatedRange = this.formData[relatedField];
          if (relatedRange && relatedRange.length === 2) {
            options.disabledDate = (time) => {
              const startTime = new Date(relatedRange[0]).getTime();
              const endTime = new Date(relatedRange[1]).getTime();
              const currentTime = time.getTime();

              if (includeToday) {
                // 包含边界日期：禁用范围外的日期
                return currentTime < startTime || currentTime > endTime;
              } else {
                // 不包含边界日期：禁用范围外及边界的日期
                return currentTime <= startTime || currentTime >= endTime;
              }
            };
          }
        }

        // 支持绝对日期约束
        if (type === 'afterDate' && field.constraints.date) {
          const constraintDate = new Date(field.constraints.date).getTime();
          options.disabledDate = (time) => {
            const currentTime = time.getTime();
            if (includeToday) {
              return currentTime < constraintDate;
            } else {
              return currentTime <= constraintDate;
            }
          };
        }

        if (type === 'beforeDate' && field.constraints.date) {
          const constraintDate = new Date(field.constraints.date).getTime();
          options.disabledDate = (time) => {
            const currentTime = time.getTime();
            if (includeToday) {
              return currentTime > constraintDate;
            } else {
              return currentTime >= constraintDate;
            }
          };
        }
      }

      return options;
    },

    // 处理日期变化
    handleDateChange(field, value) {
      this.$emit('field-change', { field: field.key, value, type: 'date' });
    },

    // 搜索
    handleSearch() {
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.searchLoading = true;
          this.$emit('search', { ...this.formData });
          
          // 模拟搜索延迟
          setTimeout(() => {
            this.searchLoading = false;
          }, 1000);
        } else {
          this.$message.error('请检查输入项');
        }
      });
    },

    // 重置
    handleReset() {
      this.$refs.searchForm.resetFields();
      this.initializeForm();
      this.$emit('reset');
    },

    // 处理额外按钮
    handleExtraButton(button) {
      if (button.handler && typeof button.handler === 'function') {
        button.handler(this.getFormData());
      }
      this.$emit('extra-button', { button, formData: { ...this.formData } });
    },

    // 获取表单数据
    getFormData() {
      return { ...this.formData };
    },

    // 设置表单数据
    setFormData(data) {
      Object.keys(data).forEach(key => {
        if (this.formData.hasOwnProperty(key)) {
          this.formData[key] = data[key];
        }
      });
    },

    // 验证表单
    validate() {
      return new Promise((resolve) => {
        this.$refs.searchForm.validate(resolve);
      });
    },

    // 清空验证
    clearValidate() {
      this.$refs.searchForm.clearValidate();
    }
  }
};
</script>

<style scoped>
/* 主容器 - 现代化卡片设计 */
.advanced-search {
  position: relative;
  padding: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

/* 悬停效果 */
.advanced-search:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

/* 装饰性背景元素 */
.advanced-search::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
}

/* 表单容器 */
.search-form {
  margin-bottom: 0;
  position: relative;
}

/* 表单项样式优化 */
.search-form :deep(.el-form-item) {
  margin-bottom: 24px;
  transition: all 0.2s ease;
}

.search-form :deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 标签样式 */
.search-form :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
  position: relative;
}

/* 输入框样式优化 */
.search-form :deep(.el-input__inner) {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.search-form :deep(.el-input__inner:focus) {
  border-color: #667eea;
  box-shadow:
    0 0 0 3px rgba(102, 126, 234, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.05);
  outline: none;
}

.search-form :deep(.el-input__inner:hover) {
  border-color: #9ca3af;
}

/* Select下拉框样式 */
.search-form :deep(.el-select) {
  width: 100%;
}

.search-form :deep(.el-select .el-input__inner) {
  cursor: pointer;
}

.search-form :deep(.el-select .el-input__suffix) {
  transition: transform 0.2s ease;
}

.search-form :deep(.el-select.is-focus .el-input__suffix) {
  transform: rotate(180deg);
}

/* 日期选择器样式 */
.search-form :deep(.el-date-editor) {
  width: 100%;
}

.search-form :deep(.el-date-editor .el-input__inner) {
  cursor: pointer;
}

/* 按钮组 - 现代化设计 */
.button-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-top: 32px;
  margin-top: 32px;
  position: relative;
}

/* 分割线优化 */
.button-group::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
}

/* 按钮样式重置 */
.button-group .el-button {
  margin: 0;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 14px;
  border-radius: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 主要按钮样式 */
.button-group .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow:
    0 4px 6px -1px rgba(102, 126, 234, 0.3),
    0 2px 4px -1px rgba(102, 126, 234, 0.2);
}

.button-group .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 12px -2px rgba(102, 126, 234, 0.4),
    0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

.button-group .el-button--primary:active {
  transform: translateY(0);
}

/* 次要按钮样式 */
.button-group .el-button--default {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  color: #6b7280;
}

.button-group .el-button--default:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .advanced-search {
    padding: 24px 20px;
    border-radius: 12px;
    margin: 0 8px;
  }

  .button-group {
    flex-direction: column;
    gap: 12px;
    padding-top: 24px;
    margin-top: 24px;
  }

  .button-group .el-button {
    width: 100%;
    max-width: 200px;
  }

  .search-form :deep(.el-form-item) {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .advanced-search {
    padding: 20px 16px;
    border-radius: 8px;
  }

  .search-form :deep(.el-input__inner) {
    padding: 10px 14px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .button-group .el-button {
    padding: 14px 24px;
    font-size: 16px;
  }
}

/* 加载状态样式 */
.advanced-search.loading {
  pointer-events: none;
  opacity: 0.7;
}

.advanced-search.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 10;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.advanced-search {
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 焦点可见性 */
.search-form :deep(.el-input__inner:focus-visible),
.button-group .el-button:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .advanced-search {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: rgba(75, 85, 99, 0.8);
    color: #f9fafb;
  }

  .search-form :deep(.el-form-item__label) {
    color: #f3f4f6;
  }

  .search-form :deep(.el-input__inner) {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .search-form :deep(.el-input__inner:focus) {
    border-color: #667eea;
    background: #4b5563;
  }

  .button-group .el-button--default {
    background: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }

  .button-group .el-button--default:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
}
</style>
