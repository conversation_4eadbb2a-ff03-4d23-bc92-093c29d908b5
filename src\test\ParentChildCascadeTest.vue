<template>
  <div class="parent-child-cascade-test">
    <h2>父子级联逻辑测试</h2>
    
    <div class="test-section">
      <h3>问题修复验证</h3>
      <el-alert
        title="修复内容"
        type="success"
        :closable="false"
        show-icon
      >
        <p><strong>修复问题</strong>: 当下级checkbox全部取消选择时，上级checkbox现在会自动取消选择</p>
        <p><strong>测试方法</strong>: 先选中父级（自动选中所有子级），然后逐个取消子级，观察父级状态变化</p>
      </el-alert>
    </div>

    <div class="test-section">
      <h3>父子级联测试</h3>
      <p class="test-desc">测试完整的父子级联逻辑：选中、取消、半选状态</p>
      <advanced-search
        :config="cascadeTestConfig"
        @search="handleSearch"
        @reset="handleReset"
        @field-change="handleFieldChange"
      />
    </div>

    <div class="status-section">
      <h3>实时状态监控</h3>
      <div class="status-grid">
        <div class="status-item">
          <h4>当前选中值:</h4>
          <pre>{{ JSON.stringify(formData.cascadeTest || [], null, 2) }}</pre>
        </div>
        <div class="status-item">
          <h4>状态分析:</h4>
          <div class="status-analysis">
            <p><strong>总选中数量:</strong> {{ selectedCount }}</p>
            <p><strong>技术部状态:</strong> {{ getTechStatus() }}</p>
            <p><strong>产品部状态:</strong> {{ getProductStatus() }}</p>
            <p><strong>销售部状态:</strong> {{ getSalesStatus() }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="test-guide">
      <h3>测试指南</h3>
      <el-collapse>
        <el-collapse-item title="测试步骤1: 父级选中测试" name="1">
          <ol>
            <li>点击"技术部"checkbox</li>
            <li>观察所有子级（前端组、后端组、移动端组、运维组）是否自动选中</li>
            <li>预期结果: ✅ 所有子级自动选中</li>
          </ol>
        </el-collapse-item>
        
        <el-collapse-item title="测试步骤2: 子级全部取消测试（关键修复）" name="2">
          <ol>
            <li>确保"技术部"及其所有子级都已选中</li>
            <li>逐个取消子级选择：先取消"前端组"</li>
            <li>观察"技术部"变为半选状态</li>
            <li>继续取消"后端组"、"移动端组"</li>
            <li>最后取消"运维组"</li>
            <li>预期结果: ✅ 当所有子级都取消后，"技术部"自动取消选中</li>
          </ol>
        </el-collapse-item>
        
        <el-collapse-item title="测试步骤3: 半选状态测试" name="3">
          <ol>
            <li>只选中"前端组"和"后端组"</li>
            <li>观察"技术部"是否显示半选状态</li>
            <li>再选中"移动端组"和"运维组"</li>
            <li>观察"技术部"是否自动变为全选状态</li>
            <li>预期结果: ✅ 半选和全选状态正确切换</li>
          </ol>
        </el-collapse-item>
        
        <el-collapse-item title="测试步骤4: 混合操作测试" name="4">
          <ol>
            <li>选中"技术部"（全选）</li>
            <li>选中"产品部"下的"产品经理"</li>
            <li>取消"技术部"下的"前端组"</li>
            <li>观察各级状态是否正确</li>
            <li>预期结果: ✅ 复杂操作下状态仍然正确</li>
          </ol>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import AdvancedSearch from '../components/AdvancedSearch.vue';

export default {
  name: 'ParentChildCascadeTest',
  components: {
    AdvancedSearch
  },
  data() {
    return {
      formData: {},
      
      cascadeTestConfig: {
        fields: [
          {
            key: 'cascadeTest',
            type: 'cascader',
            label: '部门级联测试',
            placeholder: '请选择部门（测试父子级联逻辑）',
            span: 24,
            useCustomCascader: true,
            multiple: true,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'company',
                  label: '公司总部',
                  children: [
                    {
                      value: 'tech',
                      label: '技术部',
                      children: [
                        { value: 'frontend', label: '前端组' },
                        { value: 'backend', label: '后端组' },
                        { value: 'mobile', label: '移动端组' },
                        { value: 'devops', label: '运维组' }
                      ]
                    },
                    {
                      value: 'product',
                      label: '产品部',
                      children: [
                        { value: 'pm', label: '产品经理' },
                        { value: 'ui', label: 'UI设计师' },
                        { value: 'ux', label: 'UX设计师' },
                        { value: 'research', label: '用户研究' }
                      ]
                    },
                    {
                      value: 'sales',
                      label: '销售部',
                      children: [
                        { value: 'inside', label: '内销组' },
                        { value: 'outside', label: '外销组' },
                        { value: 'support', label: '销售支持' },
                        { value: 'channel', label: '渠道管理' }
                      ]
                    },
                    {
                      value: 'hr',
                      label: '人力资源部',
                      children: [
                        { value: 'recruit', label: '招聘组' },
                        { value: 'training', label: '培训组' },
                        { value: 'compensation', label: '薪酬组' }
                      ]
                    }
                  ]
                }
              ]
            }
          }
        ],
        buttons: {
          search: '查看选中结果',
          reset: '重置测试'
        }
      }
    };
  },
  
  computed: {
    selectedCount() {
      return Array.isArray(this.formData.cascadeTest) ? this.formData.cascadeTest.length : 0;
    }
  },
  
  methods: {
    handleSearch(formData) {
      console.log('级联测试搜索:', formData);
      this.$message.success(`已选中 ${this.selectedCount} 个选项`);
    },
    
    handleReset() {
      console.log('级联测试重置');
      this.formData = {};
      this.$message.info('测试已重置');
    },
    
    handleFieldChange({ field, value, type }) {
      console.log(`字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.formData, field, value);
    },
    
    // 获取技术部状态
    getTechStatus() {
      const selected = this.formData.cascadeTest || [];
      const techItems = ['tech', 'frontend', 'backend', 'mobile', 'devops'];
      const selectedTechItems = techItems.filter(item => selected.includes(item));
      
      if (selectedTechItems.length === 0) {
        return '未选中';
      } else if (selectedTechItems.length === techItems.length) {
        return '全选';
      } else if (selectedTechItems.includes('tech')) {
        return '选中（包含父级）';
      } else {
        return `半选 (${selectedTechItems.length}/${techItems.length - 1})`;
      }
    },
    
    // 获取产品部状态
    getProductStatus() {
      const selected = this.formData.cascadeTest || [];
      const productItems = ['product', 'pm', 'ui', 'ux', 'research'];
      const selectedProductItems = productItems.filter(item => selected.includes(item));
      
      if (selectedProductItems.length === 0) {
        return '未选中';
      } else if (selectedProductItems.length === productItems.length) {
        return '全选';
      } else if (selectedProductItems.includes('product')) {
        return '选中（包含父级）';
      } else {
        return `半选 (${selectedProductItems.length}/${productItems.length - 1})`;
      }
    },
    
    // 获取销售部状态
    getSalesStatus() {
      const selected = this.formData.cascadeTest || [];
      const salesItems = ['sales', 'inside', 'outside', 'support', 'channel'];
      const selectedSalesItems = salesItems.filter(item => selected.includes(item));
      
      if (selectedSalesItems.length === 0) {
        return '未选中';
      } else if (selectedSalesItems.length === salesItems.length) {
        return '全选';
      } else if (selectedSalesItems.includes('sales')) {
        return '选中（包含父级）';
      } else {
        return `半选 (${selectedSalesItems.length}/${salesItems.length - 1})`;
      }
    }
  }
};
</script>

<style scoped>
.parent-child-cascade-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.test-desc {
  margin-bottom: 15px;
  color: #666;
  font-style: italic;
}

.status-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
}

.status-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #0066cc;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.status-item h4 {
  margin: 0 0 10px 0;
  color: #0066cc;
  font-size: 14px;
}

.status-item pre {
  background: #fff;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  max-height: 150px;
  overflow-y: auto;
}

.status-analysis p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.5;
}

.test-guide {
  padding: 20px;
  background: #fff9e6;
  border: 1px solid #ffd700;
  border-radius: 8px;
}

.test-guide h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #b8860b;
}

.test-guide ol {
  margin: 10px 0;
  padding-left: 20px;
}

.test-guide li {
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
