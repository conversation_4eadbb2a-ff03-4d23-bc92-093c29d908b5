# AdvancedSearch组件Cascader功能总结

## 概述

AdvancedSearch组件已成功集成Element UI的Cascader级联选择器，提供完整的单选、多选、异步加载和懒加载支持。

## 新增功能

### 1. Cascader字段类型支持
- ✅ 完整的Cascader组件集成
- ✅ 与现有字段类型无缝兼容
- ✅ 统一的配置接口

### 2. 单选模式
```javascript
{
  key: 'region',
  type: 'cascader',
  multiple: false,  // 单选模式
  emitPath: false,  // 只返回最后一级值
  showAllLevels: true  // 显示完整路径
}
```

### 3. 多选模式
```javascript
{
  key: 'departments',
  type: 'cascader',
  multiple: true,  // 多选模式
  checkStrictly: false,  // 父子关联
  collapseTags: true  // 折叠标签
}
```

### 4. 数据源支持
- **静态数据源**: 直接配置数据
- **异步数据源**: 函数式数据加载
- **懒加载**: 动态按需加载

### 5. 高级配置
- **自定义属性映射**: 支持不同的数据字段名
- **搜索过滤**: 支持关键词搜索
- **自定义分隔符**: 可配置显示分隔符
- **清空功能**: 支持清空选择

## 核心实现

### 1. 模板集成
```vue
<!-- Cascader类型 -->
<el-form-item 
  v-if="field.type === 'cascader'" 
  :label="field.label"
  :prop="field.key"
>
  <el-cascader
    v-model="formData[field.key]"
    :options="getCascaderOptions(field)"
    :props="getCascaderProps(field)"
    :placeholder="field.placeholder || '请选择'"
    :clearable="field.clearable !== false"
    :filterable="field.filterable"
    :show-all-levels="field.showAllLevels !== false"
    :collapse-tags="field.collapseTags"
    :separator="field.separator || ' / '"
    style="width: 100%"
    @change="handleCascaderChange(field, $event)"
  />
</el-form-item>
```

### 2. 核心方法
```javascript
// 获取cascader选项
getCascaderOptions(field) {
  if (!field.dataSource) return [];
  
  if (field.dataSource.type === 'static') {
    return field.dataSource.data || [];
  }
  
  if (field.dataSource.type === 'function') {
    return this.cascaderOptionsCache[field.key] || [];
  }
  
  return [];
}

// 获取cascader配置属性
getCascaderProps(field) {
  const defaultProps = {
    value: 'value',
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    leaf: 'leaf'
  };
  
  const userProps = field.cascaderProps || {};
  const props = { ...defaultProps, ...userProps };
  
  // 设置单选/多选
  if (field.multiple) {
    props.multiple = true;
    if (field.checkStrictly !== undefined) {
      props.checkStrictly = field.checkStrictly;
    }
  }
  
  return props;
}
```

### 3. 数据缓存
```javascript
data() {
  return {
    cascaderOptionsCache: {},  // 新增cascader选项缓存
    // ... 其他数据
  };
}
```

## 配置示例

### 1. 地区三级联动（单选）
```javascript
{
  key: 'region',
  type: 'cascader',
  label: '地区',
  multiple: false,
  emitPath: false,
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'guangdong',
        label: '广东省',
        children: [
          {
            value: 'guangzhou',
            label: '广州市',
            children: [
              { value: 'tianhe', label: '天河区' }
            ]
          }
        ]
      }
    ]
  }
}
```

### 2. 部门权限（多选）
```javascript
{
  key: 'departments',
  type: 'cascader',
  label: '部门',
  multiple: true,
  checkStrictly: false,
  emitPath: true,
  collapseTags: true,
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'tech',
        label: '技术部',
        children: [
          { value: 'frontend', label: '前端组' },
          { value: 'backend', label: '后端组' }
        ]
      }
    ]
  }
}
```

### 3. 异步懒加载
```javascript
{
  key: 'asyncCategory',
  type: 'cascader',
  label: '异步分类',
  lazy: true,
  lazyLoad: async (node, resolve) => {
    const response = await fetch(`/api/categories?parentId=${node.value || 0}`);
    const data = await response.json();
    resolve(data);
  },
  cascaderProps: {
    value: 'id',
    label: 'name',
    leaf: 'isLeaf'
  }
}
```

## 配置属性

### 基础属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `type` | String | - | 设置为 'cascader' |
| `multiple` | Boolean | false | 是否多选 |
| `emitPath` | Boolean | true | 是否返回完整路径 |
| `checkStrictly` | Boolean | false | 父子节点是否关联 |
| `showAllLevels` | Boolean | true | 是否显示完整路径 |
| `collapseTags` | Boolean | false | 多选时是否折叠标签 |
| `separator` | String | ' / ' | 分隔符 |
| `filterable` | Boolean | false | 是否可搜索 |
| `clearable` | Boolean | true | 是否可清空 |

### 懒加载属性
| 属性 | 类型 | 说明 |
|------|------|------|
| `lazy` | Boolean | 是否懒加载 |
| `lazyLoad` | Function | 懒加载函数 |

### 自定义映射
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `cascaderProps.value` | String | 'value' | 值字段名 |
| `cascaderProps.label` | String | 'label' | 标签字段名 |
| `cascaderProps.children` | String | 'children' | 子节点字段名 |
| `cascaderProps.disabled` | String | 'disabled' | 禁用字段名 |
| `cascaderProps.leaf` | String | 'leaf' | 叶子节点字段名 |

## 使用场景

### 1. 地区选择
- 省市区三级联动
- 支持搜索和清空
- 单选模式，返回区县代码

### 2. 组织架构
- 公司-部门-小组多级结构
- 多选模式，支持批量选择
- 父子关联选择

### 3. 商品分类
- 多级商品分类选择
- 异步懒加载
- 自定义数据字段映射

### 4. 权限管理
- 多级权限树选择
- 严格父子关联
- 支持任意级别选择

## 技术特性

### 1. 完整集成
- 与Element UI Cascader完全兼容
- 支持所有原生属性和事件
- 统一的配置接口

### 2. 数据源灵活
- 静态数据源
- 异步函数数据源
- 懒加载数据源

### 3. 配置丰富
- 单选/多选模式
- 自定义属性映射
- 灵活的显示配置

### 4. 性能优化
- 选项数据缓存
- 懒加载支持
- 事件优化处理

## 测试验证

### 测试步骤
1. 访问应用主页
2. 切换到"Cascader级联选择"标签
3. 测试不同配置的cascader
4. 验证单选/多选功能
5. 测试异步加载功能

### 预期结果
- ✅ Cascader组件正常渲染
- ✅ 单选模式正常工作
- ✅ 多选模式正常工作
- ✅ 异步加载正常工作
- ✅ 搜索过滤正常工作
- ✅ 事件处理正常工作

## 文档资源

- **配置指南**: `docs/CASCADER_CONFIGURATION_GUIDE.md`
- **功能总结**: `docs/CASCADER_FEATURE_SUMMARY.md`
- **示例代码**: `src/examples/CascaderExample.vue`

## 未来扩展

### 1. 功能增强
- 默认值支持
- 验证规则集成
- 更多自定义选项

### 2. 性能优化
- 虚拟滚动支持
- 更智能的缓存策略
- 批量操作优化

### 3. 用户体验
- 更好的加载状态
- 自定义渲染支持
- 无障碍访问优化

通过这次功能扩展，AdvancedSearch组件现在支持完整的级联选择功能，为用户提供了更丰富的数据选择方式。
