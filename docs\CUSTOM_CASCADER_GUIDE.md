# 自定义Cascader级联选择器指南

## 概述

为了解决Element UI原生Cascader多选功能的问题，我们开发了一个自定义的Cascader组件，使用Radio（单选）和Checkbox（多选）来实现更直观的级联选择体验。

## 核心特性

### 1. Radio单选模式
- 使用单选按钮进行选择
- 清晰的层级展示
- 支持多级级联

### 2. Checkbox多选模式
- 使用复选框进行选择
- **父级选中时，子级自动全选**
- **父级取消时，子级自动全部取消**
- 支持半选状态显示

### 3. 智能展开
- 点击箭头图标展开下级
- 支持多层级展开
- 清晰的视觉反馈

## 使用方式

### 1. 启用自定义Cascader
在字段配置中添加 `useCustomCascader: true`：

```javascript
{
  key: 'region',
  type: 'cascader',
  label: '地区选择',
  useCustomCascader: true,  // 启用自定义Cascader
  multiple: false,          // 单选模式
  dataSource: {
    type: 'static',
    data: [/* 级联数据 */]
  }
}
```

### 2. 单选配置示例
```javascript
{
  key: 'singleSelect',
  type: 'cascader',
  label: '单选模式（Radio）',
  placeholder: '请选择地区',
  span: 12,
  useCustomCascader: true,
  multiple: false,  // 单选模式
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'china',
        label: '中国',
        children: [
          {
            value: 'guangdong',
            label: '广东省',
            children: [
              { value: 'guangzhou', label: '广州市' },
              { value: 'shenzhen', label: '深圳市' }
            ]
          },
          {
            value: 'beijing',
            label: '北京市',
            children: [
              { value: 'dongcheng', label: '东城区' },
              { value: 'xicheng', label: '西城区' }
            ]
          }
        ]
      }
    ]
  }
}
```

### 3. 多选配置示例
```javascript
{
  key: 'multiSelect',
  type: 'cascader',
  label: '多选模式（Checkbox）',
  placeholder: '请选择部门',
  span: 12,
  useCustomCascader: true,
  multiple: true,  // 多选模式
  dataSource: {
    type: 'static',
    data: [
      {
        value: 'company',
        label: '公司',
        children: [
          {
            value: 'tech',
            label: '技术部',
            children: [
              { value: 'frontend', label: '前端组' },
              { value: 'backend', label: '后端组' },
              { value: 'mobile', label: '移动端组' }
            ]
          },
          {
            value: 'product',
            label: '产品部',
            children: [
              { value: 'pm', label: '产品经理' },
              { value: 'ui', label: 'UI设计师' },
              { value: 'ux', label: 'UX设计师' }
            ]
          }
        ]
      }
    ]
  }
}
```

## 核心功能

### 1. 父子级联选择
当选中父级checkbox时：
- ✅ 自动选中所有子级
- ✅ 递归选中所有后代节点
- ✅ 更新半选状态

当取消父级checkbox时：
- ✅ 自动取消所有子级
- ✅ 递归取消所有后代节点
- ✅ 清除半选状态

### 2. 半选状态
- 当部分子级被选中时，父级显示半选状态
- 视觉上用不确定状态的checkbox表示
- 智能计算和更新半选状态

### 3. 层级展示
- 清晰的多列布局
- 每列显示对应层级的选项
- 支持滚动查看更多选项

## 技术实现

### 1. 组件结构
```vue
<template>
  <div class="custom-cascader">
    <el-popover placement="bottom-start" width="400" trigger="click">
      <div class="cascader-panel">
        <div class="cascader-menu" v-for="level in levels">
          <div class="menu-items">
            <div class="menu-item" v-for="option in level">
              <!-- Radio单选 -->
              <el-radio v-if="!multiple" v-model="selectedValues[levelIndex]" :label="option.value">
                {{ option.label }}
              </el-radio>
              
              <!-- Checkbox多选 -->
              <el-checkbox v-else :value="isChecked(option.value)" @change="handleCheckboxChange(option, $event)" :indeterminate="isIndeterminate(option.value)">
                {{ option.label }}
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
      
      <el-input slot="reference" :value="displayValue" readonly />
    </el-popover>
  </div>
</template>
```

### 2. 核心方法

#### 多选处理
```javascript
handleCheckboxChange(option, levelIndex, checked) {
  if (checked) {
    this.checkedValues.add(option.value);
    // 选中父级时，自动选中所有子级
    this.selectAllChildren(option);
  } else {
    this.checkedValues.delete(option.value);
    // 取消选中父级时，取消所有子级
    this.unselectAllChildren(option);
  }
  
  this.updateIndeterminateState();
  this.emitChange();
}
```

#### 递归选中子级
```javascript
selectAllChildren(option) {
  if (option.children && option.children.length > 0) {
    option.children.forEach(child => {
      this.checkedValues.add(child.value);
      this.selectAllChildren(child);  // 递归处理
    });
  }
}
```

#### 半选状态更新
```javascript
updateIndeterminateState() {
  this.indeterminateValues.clear();
  this.updateNodeIndeterminate(this.options);
}

updateNodeIndeterminate(nodes) {
  nodes.forEach(node => {
    if (node.children && node.children.length > 0) {
      const checkedChildren = node.children.filter(child => 
        this.checkedValues.has(child.value) || this.indeterminateValues.has(child.value)
      );
      
      if (checkedChildren.length > 0 && checkedChildren.length < node.children.length) {
        this.indeterminateValues.add(node.value);  // 半选状态
      } else if (checkedChildren.length === node.children.length) {
        this.checkedValues.add(node.value);  // 全选状态
      }
      
      this.updateNodeIndeterminate(node.children);
    }
  });
}
```

## 返回值格式

### 单选模式
```javascript
// 选择路径: 中国 > 广东省 > 广州市
// 返回值: ['china', 'guangdong', 'guangzhou']
```

### 多选模式
```javascript
// 选择: 技术部下的前端组和后端组
// 返回值: ['frontend', 'backend']

// 选择: 整个技术部（自动包含所有子级）
// 返回值: ['tech', 'frontend', 'backend', 'mobile']
```

## 样式特性

### 1. 现代化设计
- 清晰的层级分隔
- 舒适的间距和字体
- 悬停效果和过渡动画

### 2. 响应式布局
- 自适应宽度
- 滚动条优化
- 移动端友好

### 3. 视觉反馈
- 选中状态高亮
- 半选状态显示
- 展开/收起动画

## 使用场景

### 1. 地区选择
- 国家 > 省份 > 城市 > 区县
- 单选模式，选择具体地址

### 2. 组织架构
- 公司 > 部门 > 小组 > 个人
- 多选模式，批量选择部门

### 3. 权限管理
- 模块 > 功能 > 操作权限
- 多选模式，父级选中自动包含子权限

### 4. 商品分类
- 大类 > 中类 > 小类 > 具体商品
- 支持单选和多选

## 优势对比

| 特性 | 原生Cascader | 自定义Cascader |
|------|-------------|----------------|
| 多选支持 | 有问题 | ✅ 完美支持 |
| 父子关联 | 复杂配置 | ✅ 自动处理 |
| 视觉反馈 | 一般 | ✅ 清晰直观 |
| 半选状态 | 不支持 | ✅ 智能显示 |
| 自定义性 | 有限 | ✅ 高度可定制 |

## 最佳实践

### 1. 数据结构
- 保持一致的数据格式
- 合理设置层级深度
- 提供清晰的标签文本

### 2. 用户体验
- 合理的默认展开状态
- 清晰的操作提示
- 适当的加载状态

### 3. 性能优化
- 大数据量时考虑虚拟滚动
- 合理的缓存策略
- 避免过深的递归

通过这个自定义Cascader组件，我们解决了原生组件的多选问题，提供了更直观、更强大的级联选择体验。
