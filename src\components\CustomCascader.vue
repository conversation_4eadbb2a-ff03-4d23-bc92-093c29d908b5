<template>
  <div class="custom-cascader">
    <el-popover
      placement="bottom-start"
      :width="popoverWidth"
      trigger="click"
      v-model="visible"
    >
      <div class="cascader-panel">
        <div class="cascader-menu" v-for="(level, levelIndex) in levels" :key="levelIndex">
          <div class="menu-title">{{ getLevelTitle(levelIndex) }}</div>
          <div class="menu-items">
            <div
              v-for="option in level"
              :key="option.value"
              class="menu-item"
              :class="{
                'has-children': option.children && option.children.length > 0,
                'selected': isOptionSelected(option.value, levelIndex)
              }"
              @click="handleItemClick(option, levelIndex, $event)"
            >
              <!-- 单选模式 -->
              <el-radio
                v-if="!multiple"
                v-model="selectedValues[levelIndex]"
                :label="option.value"
                @change="handleRadioChange(option, levelIndex)"
                @click.stop
              >
                {{ option.label }}
              </el-radio>

              <!-- 多选模式 -->
              <el-checkbox
                v-else
                v-model="checkboxStates[option.value]"
                @change="handleCheckboxChange(option, levelIndex, $event)"
                :indeterminate="isIndeterminate(option.value)"
                @click.native.stop="handleCheckboxClick(option, levelIndex, $event)"
                class="cascader-checkbox"
              >
                {{ option.label }}
              </el-checkbox>

              <!-- 展开按钮 -->
              <i
                v-if="option.children && option.children.length > 0"
                class="el-icon-arrow-right expand-icon"
                :class="{ 'expanded': isExpanded(option.value, levelIndex) }"
                @click.stop="toggleExpand(option, levelIndex)"
              ></i>
            </div>
          </div>
        </div>
      </div>
      
      <el-input
        slot="reference"
        :value="displayValue"
        :placeholder="placeholder"
        readonly
        suffix-icon="el-icon-arrow-down"
        @click="visible = true"
      />
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'CustomCascader',
  props: {
    value: {
      type: [Array, String, Number],
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    separator: {
      type: String,
      default: ' / '
    },
    props: {
      type: Object,
      default: () => ({
        value: 'value',
        label: 'label',
        children: 'children'
      })
    }
  },
  
  data() {
    return {
      visible: false,
      levels: [],
      selectedValues: [],
      expandedNodes: [],
      checkedValues: new Set(),
      indeterminateValues: new Set(),
      checkboxStates: {}  // 新增：用于v-model绑定的checkbox状态
    };
  },
  
  computed: {
    displayValue() {
      if (this.multiple) {
        const labels = this.getSelectedLabels();
        return labels.length > 0 ? labels.join(', ') : '';
      } else {
        return this.getSelectedPath().join(this.separator);
      }
    },

    // 动态计算popover宽度
    popoverWidth() {
      const baseWidth = 160;  // 每列的基础宽度
      const levelCount = this.levels.length;
      const minWidth = 200;   // 最小宽度
      const maxWidth = 600;   // 最大宽度

      const calculatedWidth = Math.max(minWidth, Math.min(maxWidth, baseWidth * levelCount + 20));
      return calculatedWidth;
    }
  },
  
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.initializeSelection(newVal);
      }
    },
    
    options: {
      immediate: true,
      handler(newVal) {
        this.initializeLevels(newVal);
      }
    }
  },
  
  methods: {
    // 初始化级联层级
    initializeLevels(options) {
      this.levels = [options];
      this.expandedNodes = [];

      // 如果是多选模式，初始化checkbox状态
      if (this.multiple) {
        this.syncCheckboxStates();
      }
    },
    
    // 初始化选中状态
    initializeSelection(value) {
      console.log('初始化选中状态:', value);

      if (this.multiple) {
        this.checkedValues = new Set(Array.isArray(value) ? value : (value ? [value] : []));
        // 同步更新checkboxStates
        this.syncCheckboxStates();
        this.updateIndeterminateState();
      } else {
        // 单选模式：value可能是路径数组或单个值
        if (Array.isArray(value)) {
          this.selectedValues = [...value];
        } else if (value !== undefined && value !== null && value !== '') {
          this.selectedValues = [value];
        } else {
          this.selectedValues = [];
        }
        this.expandToSelection();
      }
    },
    
    // 获取层级标题
    getLevelTitle(levelIndex) {
      const titles = ['一级选择', '二级选择', '三级选择', '四级选择'];
      return titles[levelIndex] || `${levelIndex + 1}级选择`;
    },
    
    // 单选变化处理
    handleRadioChange(option, levelIndex) {
      console.log('Radio变化:', option.label, 'levelIndex:', levelIndex);

      // 更新选中值
      this.$set(this.selectedValues, levelIndex, option.value);

      // 清除后续层级的选中值和层级数据
      this.selectedValues.splice(levelIndex + 1);
      this.levels.splice(levelIndex + 1);

      // 清除后续层级的展开状态
      this.expandedNodes = this.expandedNodes.filter(node => node.level <= levelIndex);

      // 如果有子级，展开下一层
      if (option.children && option.children.length > 0) {
        this.levels.push(option.children);
        // 添加到展开节点
        this.expandedNodes.push({ value: option.value, level: levelIndex });
      }

      this.emitChange();
    },
    
    // Checkbox点击处理（原生点击事件）
    handleCheckboxClick(option, levelIndex, event) {
      console.log('Checkbox原生点击:', option.label);
      // 阻止事件冒泡，但不阻止checkbox本身的功能
      event.stopPropagation();
    },

    // 多选变化处理
    handleCheckboxChange(option, levelIndex, checked) {
      console.log('Checkbox变化:', option.label, 'checked:', checked);

      if (checked) {
        this.checkedValues.add(option.value);
        // 选中父级时，自动选中所有子级
        this.selectAllChildren(option);
      } else {
        this.checkedValues.delete(option.value);
        // 取消选中父级时，取消所有子级
        this.unselectAllChildren(option);
        // 同时从半选状态中移除
        this.indeterminateValues.delete(option.value);
      }

      // 同步更新checkboxStates
      this.syncCheckboxStates();

      // 更新父级的半选状态
      this.updateParentIndeterminateState();
      this.emitChange();
    },
    
    // 选中所有子级
    selectAllChildren(option) {
      if (option.children && option.children.length > 0) {
        option.children.forEach(child => {
          this.checkedValues.add(child.value);
          this.selectAllChildren(child);
        });
      }
    },

    // 取消选中所有子级
    unselectAllChildren(option) {
      if (option.children && option.children.length > 0) {
        option.children.forEach(child => {
          this.checkedValues.delete(child.value);
          this.unselectAllChildren(child);
        });
      }
    },

    // 同步checkbox状态
    syncCheckboxStates() {
      // 清空现有状态
      this.checkboxStates = {};

      // 递归设置所有选项的checkbox状态
      this.setCheckboxStatesRecursive(this.options);
    },

    // 递归设置checkbox状态
    setCheckboxStatesRecursive(nodes) {
      nodes.forEach(node => {
        this.$set(this.checkboxStates, node.value, this.checkedValues.has(node.value));

        if (node.children && node.children.length > 0) {
          this.setCheckboxStatesRecursive(node.children);
        }
      });
    },
    
    // 处理行点击事件
    handleItemClick(option, levelIndex, event) {
      // 检查点击的是否是checkbox区域
      if (this.multiple && event && event.target) {
        const isCheckboxArea = event.target.closest('.el-checkbox') ||
                              event.target.classList.contains('el-checkbox') ||
                              event.target.closest('.cascader-checkbox');

        // 如果点击的是checkbox区域，不处理行点击，让checkbox自己处理
        if (isCheckboxArea) {
          return;
        }

        // 非checkbox区域的点击：切换checkbox状态
        const currentChecked = this.checkedValues.has(option.value);

        // 直接更新checkboxStates来触发v-model
        this.$set(this.checkboxStates, option.value, !currentChecked);

        // 然后调用change处理
        this.handleCheckboxChange(option, levelIndex, !currentChecked);
      } else if (!this.multiple) {
        // 单选模式：选中radio并展开下级
        this.$set(this.selectedValues, levelIndex, option.value);
        this.handleRadioChange(option, levelIndex);
      }

      // 如果有子级，展开下一层
      if (option.children && option.children.length > 0) {
        this.expandToLevel(option, levelIndex);
      }
    },

    // 展开到指定层级
    expandToLevel(option, levelIndex) {
      // 清除后续层级
      this.levels.splice(levelIndex + 1);

      // 清除后续层级的展开状态
      this.expandedNodes = this.expandedNodes.filter(node => node.level <= levelIndex);

      // 添加新层级
      if (option.children && option.children.length > 0) {
        this.levels.push(option.children);
        // 添加到展开节点
        const existingIndex = this.expandedNodes.findIndex(node =>
          node.value === option.value && node.level === levelIndex
        );
        if (existingIndex === -1) {
          this.expandedNodes.push({ value: option.value, level: levelIndex });
        }
      }
    },

    // 检查选项是否被选中（用于样式）
    isOptionSelected(value, levelIndex) {
      if (this.multiple) {
        return this.checkedValues.has(value);
      } else {
        return this.selectedValues[levelIndex] === value;
      }
    },

    // 更新半选状态
    updateIndeterminateState() {
      this.indeterminateValues.clear();
      this.updateNodeIndeterminate(this.options);
      // 更新半选状态后，同步checkbox状态
      if (this.multiple) {
        this.syncCheckboxStates();
      }
    },

    // 更新父级半选状态（与updateIndeterminateState相同）
    updateParentIndeterminateState() {
      this.updateIndeterminateState();
    },
    
    // 递归更新节点半选状态
    updateNodeIndeterminate(nodes) {
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          const checkedChildren = node.children.filter(child => 
            this.checkedValues.has(child.value) || this.indeterminateValues.has(child.value)
          );
          
          if (checkedChildren.length > 0 && checkedChildren.length < node.children.length) {
            this.indeterminateValues.add(node.value);
          } else if (checkedChildren.length === node.children.length) {
            // 所有子级都选中，父级也应该选中
            this.checkedValues.add(node.value);
          }
          
          this.updateNodeIndeterminate(node.children);
        }
      });
    },
    
    // 检查是否选中
    isChecked(value) {
      return this.checkedValues.has(value);
    },

    // 检查是否半选
    isIndeterminate(value) {
      return this.indeterminateValues.has(value);
    },
    
    // 检查是否展开
    isExpanded(value, levelIndex) {
      return this.expandedNodes.some(node => 
        node.value === value && node.level === levelIndex
      );
    },
    
    // 切换展开状态
    toggleExpand(option, levelIndex) {
      const nodeKey = { value: option.value, level: levelIndex };
      const existingIndex = this.expandedNodes.findIndex(node => 
        node.value === option.value && node.level === levelIndex
      );
      
      if (existingIndex > -1) {
        // 收起
        this.expandedNodes.splice(existingIndex, 1);
        this.levels.splice(levelIndex + 1);
      } else {
        // 展开
        this.expandedNodes.push(nodeKey);
        if (option.children && option.children.length > 0) {
          // 确保只有一个下级层级
          this.levels.splice(levelIndex + 1);
          this.levels.push(option.children);
        }
      }
    },
    
    // 展开到选中项
    expandToSelection() {
      // 单选模式下，根据选中路径展开层级
      if (!this.multiple && this.selectedValues.length > 0) {
        let currentLevel = this.options;
        this.expandedNodes = [];

        for (let i = 0; i < this.selectedValues.length; i++) {
          const value = this.selectedValues[i];
          const option = currentLevel.find(opt => opt.value === value);

          if (option) {
            // 添加到展开节点
            this.expandedNodes.push({ value: option.value, level: i });

            // 如果有子级且不是最后一级，展开下一层
            if (option.children && option.children.length > 0 && i < this.selectedValues.length - 1) {
              // 确保levels数组有足够的长度
              while (this.levels.length <= i + 1) {
                this.levels.push([]);
              }
              this.levels[i + 1] = option.children;
              currentLevel = option.children;
            }
          }
        }
      }
    },
    
    // 获取选中的标签
    getSelectedLabels() {
      const labels = [];
      this.findSelectedLabels(this.options, labels);
      return labels;
    },
    
    // 递归查找选中的标签
    findSelectedLabels(nodes, labels) {
      nodes.forEach(node => {
        if (this.checkedValues.has(node.value)) {
          labels.push(node.label);
        }
        if (node.children) {
          this.findSelectedLabels(node.children, labels);
        }
      });
    },
    
    // 获取选中路径
    getSelectedPath() {
      const path = [];
      let currentLevel = this.options;
      
      for (const value of this.selectedValues) {
        const option = currentLevel.find(opt => opt.value === value);
        if (option) {
          path.push(option.label);
          currentLevel = option.children || [];
        }
      }
      
      return path;
    },
    
    // 发出变化事件
    emitChange() {
      let value;
      
      if (this.multiple) {
        value = Array.from(this.checkedValues);
      } else {
        value = this.selectedValues.filter(v => v !== undefined);
      }
      
      this.$emit('input', value);
      this.$emit('change', value);
    }
  }
};
</script>

<style scoped>
.custom-cascader {
  width: 100%;
}

.cascader-panel {
  display: flex;
  max-height: 300px;
  overflow: hidden;
  width: 100%;
}

.cascader-menu {
  flex: none;  /* 不使用flex自适应 */
  width: 160px;  /* 固定宽度 */
  border-right: 1px solid #e4e7ed;
  min-width: 160px;
  max-width: 160px;
}

.cascader-menu:last-child {
  border-right: none;
}

.menu-title {
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
  font-weight: 600;
}

.menu-items {
  max-height: 250px;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item.selected {
  background-color: #ecf5ff;
  color: #409eff;
}

.menu-item.has-children {
  padding-right: 30px;
}

.menu-item:active {
  background-color: #e6f7ff;
}

.expand-icon {
  position: absolute;
  right: 8px;
  color: #c0c4cc;
  transition: transform 0.2s;
  cursor: pointer;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.expand-icon:hover {
  color: #409eff;
}

/* 单选框和复选框样式调整 */
.menu-item :deep(.el-radio),
.menu-item :deep(.el-checkbox) {
  width: 100%;
  margin-right: 0;
  pointer-events: auto;  /* 确保可以点击 */
}

.menu-item :deep(.el-radio__label),
.menu-item :deep(.el-checkbox__label) {
  padding-left: 8px;
  font-size: 14px;
  line-height: 1.5;
  cursor: pointer;  /* 确保标签也可以点击 */
}

/* Checkbox特殊样式优化 */
.cascader-checkbox {
  width: 100% !important;
  display: flex !important;
  align-items: center;
}

.cascader-checkbox :deep(.el-checkbox__input) {
  margin-right: 8px;
  flex-shrink: 0;
}

.cascader-checkbox :deep(.el-checkbox__label) {
  flex: 1;
  padding-left: 0 !important;
  cursor: pointer;
}

/* 滚动条样式 */
.menu-items::-webkit-scrollbar {
  width: 6px;
}

.menu-items::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.menu-items::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.menu-items::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
