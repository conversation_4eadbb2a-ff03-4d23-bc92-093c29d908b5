<template>
  <div class="advanced-table">
    <!-- 表格工具栏 -->
    <div v-if="config.toolbar" class="table-toolbar">
      <!--<div class="toolbar-left">
        <slot name="toolbar-left">
          <el-button
            v-for="button in config.toolbar.buttons"
            :key="button.key"
            :type="button.type || 'default'"
            :size="button.size || 'small'"
            :icon="button.icon"
            @click="handleToolbarButton(button)"
          >
            {{ button.label }}
          </el-button>
        </slot>
      </div>-->
      
      <div class="toolbar-right">
          <el-button
            v-for="button in config.toolbar.buttons"
            :key="button.key"
            :type="button.type || 'default'"
            :size="button.size || 'small'"
            :icon="button.icon"
            @click="handleToolbarButton(button)"
          >
            {{ button.label }}
          </el-button>
        <slot name="toolbar-right">
          <!-- 列显示隐藏控制 -->
          <template v-if="config.columnControl">
            <!-- 下拉菜单模式 -->
            <el-dropdown
              v-if="!config.columnControl.dialog"
              trigger="click"
              @command="handleColumnControl"
            >
              <el-button size="small" type="text">
                列设置 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="column in allColumns"
                  :key="column.prop"
                  :command="{ action: 'toggle', column }"
                >
                  <el-checkbox
                    :value="!column.hidden"
                    @change="toggleColumn(column)"
                  >
                    {{ column.label }}
                  </el-checkbox>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- 对话框模式 -->
            <el-button
              v-else
              size="tiny"
              type="text"
              icon="el-icon-setting"
              @click="showColumnDialog = true"
            >
              列设置
            </el-button>
          </template>
        </slot>
      </div>
    </div>

    <!-- 主表格 -->
    <el-table
      ref="table"
      :data="tableData"
      :height="config.height"
      :max-height="config.maxHeight"
      :stripe="config.stripe"
      :border="config.border"
      :size="config.size"
      :fit="config.fit !== false"
      :show-header="config.showHeader !== false"
      :highlight-current-row="config.highlightCurrentRow"
      :row-class-name="getRowClassName"
      :row-style="getRowStyle"
      :cell-class-name="getCellClassName"
      :cell-style="getCellStyle"
      :span-method="config.spanMethod || handleSpanMethod"
      :show-summary="config.showSummary"
      :sum-text="config.sumText || '合计'"
      :summary-method="config.summaryMethod || handleSummaryMethod"
      :row-key="config.rowKey"
      :default-sort="config.defaultSort"
      :tooltip-effect="config.tooltipEffect || 'dark'"
      :empty-text="config.emptyText || '暂无数据'"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblclick"
      @current-change="handleCurrentChange"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange"
      @expand-change="handleExpandChange"
      @header-click="handleHeaderClick"
      @header-contextmenu="handleHeaderContextmenu"
      @row-contextmenu="handleRowContextmenu"
    >
      <!-- 动态生成列 -->
      <template v-for="column in visibleColumns">
        <!-- 选择列 -->
        <el-table-column
          v-if="column.type === 'selection'"
          :key="column.prop"
          type="selection"
          :width="column.width || 55"
          :fixed="column.fixed"
          :selectable="column.selectable"
          :reserve-selection="column.reserveSelection"
        />
        
        <!-- 索引列 -->
        <el-table-column
          v-else-if="column.type === 'index'"
          :key="column.prop"
          type="index"
          :label="column.label || '序号'"
          :width="column.width || 60"
          :fixed="column.fixed"
          :index="column.index"
        />
        
        <!-- 展开列 -->
        <el-table-column
          v-else-if="column.type === 'expand'"
          :key="column.prop"
          type="expand"
          :width="column.width || 50"
          :fixed="column.fixed"
        >
          <template slot-scope="scope">
            <slot name="expand" :row="scope.row" :$index="scope.$index">
              <div v-html="getExpandContent(scope.row, scope.$index)"></div>
            </slot>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column
          v-else-if="column.type === 'action'"
          :key="column.prop"
          :label="column.label || '操作'"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :align="column.align || 'center'"
          :class-name="column.className"
        >
          <template slot-scope="scope">
            <slot name="action" :row="scope.row" :$index="scope.$index">
              <el-dropdown
                v-if="column.dropdown"
                trigger="click"
                @command="(command) => handleActionCommand(command, scope.row, scope.$index)"
              >
                <el-button type="text" size="small">
                  {{ column.dropdown.text || '操作' }}
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="action in getRowActions(column.dropdown.actions, scope.row, scope.$index)"
                    :key="action.key"
                    :command="{ action: action.key, row: scope.row, index: scope.$index }"
                    :disabled="action.disabled"
                    :divided="action.divided"
                  >
                    <i v-if="action.icon" :class="action.icon"></i>
                    {{ action.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              
              <template v-else>
                <el-button
                  v-for="action in getRowActions(column.actions, scope.row, scope.$index)"
                  :key="action.key"
                  :type="action.type || 'text'"
                  :size="action.size || 'small'"
                  :icon="action.icon"
                  :disabled="action.disabled"
                  @click="handleAction(action.key, scope.row, scope.$index)"
                >
                  {{ action.label }}
                </el-button>
              </template>
            </slot>
          </template>
        </el-table-column>
        
        <!-- 普通数据列 -->
        <el-table-column
          v-else
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :sort-method="column.sortMethod"
          :sort-by="column.sortBy"
          :resizable="column.resizable !== false"
          :formatter="column.formatter"
          :show-overflow-tooltip="column.showOverflowTooltip"
          :align="column.align"
          :header-align="column.headerAlign"
          :class-name="column.className"
          :label-class-name="column.labelClassName"
          :filters="column.filters"
          :filter-placement="column.filterPlacement"
          :filter-multiple="column.filterMultiple"
          :filter-method="column.filterMethod"
          :filtered-value="column.filteredValue"
        >
          <template slot-scope="scope">
            <slot :name="column.prop" :row="scope.row" :column="column" :$index="scope.$index">
              <span v-html="getCellContent(scope.row, column, scope.$index)"></span>
            </slot>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <div v-if="config.pagination" class="table-pagination">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="config.pagination.pageSizes || [10, 20, 50, 100]"
        :page-size="pageSize"
        :total="internalTotal"
        :layout="config.pagination.layout || 'total, sizes, prev, pager, next, jumper'"
        :background="config.pagination.background !== false"
        :small="config.pagination.small"
        :hide-on-single-page="config.pagination.hideOnSinglePage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>

    <!-- 列设置对话框 -->
    <el-dialog
      title="列设置"
      :visible.sync="showColumnDialog"
      size="tiny"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="column-setting-dialog"
    >
      <div class="column-setting-content">
        <div class="setting-header">
          <el-button
            size="mini"
            type="text"
            @click="selectAllColumnsInDialog"
          >
            全选
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="unselectAllColumnsInDialog"
          >
            全不选
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="resetColumnsInDialog"
          >
            重置
          </el-button>
        </div>

        <div class="column-list" ref="columnList">
          <div
            v-for="(column, index) in allColumns"
            :key="column.prop || column.type || index"
            class="column-item"
            :class="{ 'disabled': !column.controllable }"
          >
            <el-checkbox
              :value="!column.hidden"
              :disabled="!column.controllable"
              @change="toggleColumnInDialog(column)"
            >
              <span class="column-label">{{ column.label || column.type }}</span>
            </el-checkbox>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

export default {
  name: 'AdvancedTable',
  
  props: {
    config: {
      type: Object,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    // 服务端分页总记录数
    total: {
      type: Number,
      default: 0
    }
  },
  
  data() {
    return {
      currentPage: 1,
      pageSize: 20,
      internalTotal: 0,
      allColumns: [],
      tableData: [],
      showColumnDialog: false
    };
  },
  
  computed: {
    visibleColumns() {
      return this.allColumns.filter(column => !column.hidden);
    }
  },
  
  watch: {
    data: {
      handler(newData) {
        this.updateTableData(newData);
      },
      immediate: true
    },

    'config.columns': {
      handler(newColumns) {
        this.initializeColumns(newColumns);
      },
      immediate: true
    },


  },
  
  mounted() {
    this.initializeTable();
  },


  
  methods: {
    // 初始化表格
    initializeTable() {
      if (this.config.pagination) {
        this.currentPage = this.config.pagination.currentPage || 1;
        this.pageSize = this.config.pagination.pageSize || 20;
      }
    },
    
    // 初始化列配置
    initializeColumns(columns) {
      if (!columns) return;

      this.allColumns = columns.map(column => ({
        ...column,
        hidden: column.hidden || false,
        originalHidden: column.hidden || false,  // 保存原始状态用于重置
        controllable: this.isColumnControllable(column)
      }));
    },

    // 判断列是否可控制
    isColumnControllable(column) {
      // 某些列类型默认不可控制
      const uncontrollableTypes = ['selection', 'index'];
      if (uncontrollableTypes.includes(column.type)) {
        return false;
      }

      // 检查配置中的不可控制列
      if (this.config.columnControl && this.config.columnControl.uncontrollable) {
        const uncontrollable = this.config.columnControl.uncontrollable;
        if (Array.isArray(uncontrollable)) {
          return !uncontrollable.includes(column.prop);
        }
      }

      return true;
    },
    
    // 更新表格数据
    updateTableData(data) {
      if (this.config.pagination && this.config.pagination.serverSide !== true) {
        // 客户端分页
        this.internalTotal = data.length;
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        this.tableData = data.slice(start, end);
      } else {
        // 服务端分页或无分页
        this.tableData = data;
        // 服务端分页时，优先使用传入的total prop，其次使用config中的total，最后使用data长度
        if (this.config.pagination && this.config.pagination.serverSide) {
          this.internalTotal = this.total || this.config.pagination.total || data.length;
        } else {
          this.internalTotal = data.length;
        }
      }
    },

    // 获取单元格内容
    getCellContent(row, column, index) {
      let value = this.getCellValue(row, column.prop);

      // 应用数据转换器
      if (column.transformer) {
        if (typeof column.transformer === 'function') {
          value = column.transformer(value, row, column, index);
        } else if (typeof column.transformer === 'object') {
          // 映射转换器
          value = column.transformer[value] || value;
        }
      }

      // 应用格式化器
      if (column.formatter && typeof column.formatter === 'function') {
        value = column.formatter(row, column, value, index);
      }

      return value;
    },

    // 获取单元格值
    getCellValue(row, prop) {
      if (!prop) return '';

      // 支持嵌套属性，如 'user.name'
      const keys = prop.split('.');
      let value = row;

      for (const key of keys) {
        if (value && typeof value === 'object') {
          value = value[key];
        } else {
          return '';
        }
      }

      return value;
    },

    // 获取行样式类名
    getRowClassName({ row, rowIndex }) {
      let className = '';

      if (this.config.rowClassName) {
        if (typeof this.config.rowClassName === 'function') {
          className = this.config.rowClassName({ row, rowIndex });
        } else {
          className = this.config.rowClassName;
        }
      }

      return className;
    },

    // 获取行样式
    getRowStyle({ row, rowIndex }) {
      if (this.config.rowStyle) {
        if (typeof this.config.rowStyle === 'function') {
          return this.config.rowStyle({ row, rowIndex });
        } else {
          return this.config.rowStyle;
        }
      }
      return {};
    },

    // 获取单元格样式类名
    getCellClassName({ row, column, rowIndex, columnIndex }) {
      if (this.config.cellClassName) {
        if (typeof this.config.cellClassName === 'function') {
          return this.config.cellClassName({ row, column, rowIndex, columnIndex });
        } else {
          return this.config.cellClassName;
        }
      }
      return '';
    },

    // 获取单元格样式
    getCellStyle({ row, column, rowIndex, columnIndex }) {
      if (this.config.cellStyle) {
        if (typeof this.config.cellStyle === 'function') {
          return this.config.cellStyle({ row, column, rowIndex, columnIndex });
        } else {
          return this.config.cellStyle;
        }
      }
      return {};
    },

    // 处理单元格合并
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.config.spanMethod) {
        return this.config.spanMethod({ row, column, rowIndex, columnIndex });
      }

      // 默认合并逻辑
      if (this.config.mergeColumns && this.config.mergeColumns.includes(column.property)) {
        // 简单的相同值合并逻辑
        const currentValue = this.getCellValue(row, column.property);
        let rowspan = 1;
        let colspan = 1;

        // 向下查找相同值
        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.getCellValue(this.tableData[i], column.property) === currentValue) {
            rowspan++;
          } else {
            break;
          }
        }

        // 检查是否是合并的起始行
        if (rowIndex > 0) {
          const prevValue = this.getCellValue(this.tableData[rowIndex - 1], column.property);
          if (prevValue === currentValue) {
            return { rowspan: 0, colspan: 0 };
          }
        }

        return { rowspan, colspan };
      }

      return { rowspan: 1, colspan: 1 };
    },

    // 处理合计行
    handleSummaryMethod(param) {
      if (this.config.summaryMethod) {
        return this.config.summaryMethod(param);
      }

      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.config.sumText || '合计';
          return;
        }

        const values = data.map(item => Number(this.getCellValue(item, column.property)));
        const precisions = [];
        let notNumber = true;

        values.forEach(value => {
          if (!isNaN(value)) {
            notNumber = false;
            const decimal = ('' + value).split('.')[1];
            precisions.push(decimal ? decimal.length : 0);
          }
        });

        const precision = Math.max.apply(null, precisions);

        if (!notNumber) {
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = precision > 0 ? sum.toFixed(precision) : sum;
        } else {
          sums[index] = '';
        }
      });

      return sums;
    },

    // 获取展开内容
    getExpandContent(row, index) {
      if (this.config.expandContent) {
        if (typeof this.config.expandContent === 'function') {
          return this.config.expandContent(row, index);
        } else {
          return this.config.expandContent;
        }
      }
      return '';
    },

    // 获取行操作按钮
    getRowActions(actions, row, index) {
      if (!actions) return [];

      return actions.filter(action => {
        if (action.show !== undefined) {
          if (typeof action.show === 'function') {
            return action.show(row, index);
          } else {
            return action.show;
          }
        }
        return true;
      }).map(action => ({
        ...action,
        disabled: action.disabled && (
          typeof action.disabled === 'function'
            ? action.disabled(row, index)
            : action.disabled
        )
      }));
    },

    // 列显示隐藏控制
    toggleColumn(column) {
      column.hidden = !column.hidden;
      this.$emit('column-change', {
        column,
        visible: !column.hidden,
        visibleColumns: this.visibleColumns
      });
    },

    // 对话框中切换列显示隐藏
    toggleColumnInDialog(column) {
      if (!column.controllable) return;

      column.hidden = !column.hidden;

      // 触发列变化事件
      this.$emit('column-change', {
        column,
        visible: !column.hidden,
        visibleColumns: this.visibleColumns
      });
    },

    // 对话框中全选列
    selectAllColumnsInDialog() {
      this.allColumns.forEach(col => {
        if (col.controllable) {
          col.hidden = false;
        }
      });

      // 触发列变化事件
      this.$emit('column-change', {
        columns: this.allColumns,
        visibleColumns: this.visibleColumns,
        action: 'select-all'
      });
    },

    // 对话框中全不选列
    unselectAllColumnsInDialog() {
      this.allColumns.forEach(col => {
        if (col.controllable) {
          col.hidden = true;
        }
      });

      // 触发列变化事件
      this.$emit('column-change', {
        columns: this.allColumns,
        visibleColumns: this.visibleColumns,
        action: 'unselect-all'
      });
    },

    // 对话框中重置列设置
    resetColumnsInDialog() {
      // 重置为初始状态
      this.allColumns.forEach(col => {
        if (col.controllable) {
          col.hidden = col.originalHidden || false;
        }
      });

      // 触发列变化事件
      this.$emit('column-change', {
        columns: this.allColumns,
        visibleColumns: this.visibleColumns,
        action: 'reset'
      });
    },





    // 导出表格数据
    exportTableData(format = 'xlsx', filename = 'table-data') {
      try {
        // 获取可见列
        const visibleColumns = this.visibleColumns.filter(col =>
          col.type !== 'selection' &&
          col.type !== 'index' &&
          col.type !== 'action' &&
          col.type !== 'expand'
        );

        // 准备表头
        const headers = visibleColumns.map(col => col.label || col.prop);

        // 准备数据
        const data = this.tableData.map(row => {
          return visibleColumns.map(col => {
            let value = this.getCellValue(row, col.prop);

            // 应用转换器
            if (col.transformer) {
              if (typeof col.transformer === 'function') {
                value = col.transformer(value, row, col);
              } else if (typeof col.transformer === 'object') {
                value = col.transformer[value] || value;
              }
            }

            // 应用格式化器
            if (col.formatter && typeof col.formatter === 'function') {
              value = col.formatter(row, col, value);
            }

            // 清理HTML标签
            if (typeof value === 'string') {
              value = value.replace(/<[^>]*>/g, '');
            }

            return value || '';
          });
        });

        // 合并表头和数据
        const exportData = [headers, ...data];

        if (format === 'csv') {
          this.exportToCSV(exportData, filename);
        } else {
          this.exportToExcel(exportData, filename, headers);
        }

        this.$message.success(`导出${format.toUpperCase()}成功`);
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      }
    },

    // 导出为CSV
    exportToCSV(data, filename) {
      const csvContent = data.map(row =>
        row.map(cell => {
          // 处理包含逗号、引号或换行符的单元格
          const cellStr = String(cell || '');
          if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
            return `"${cellStr.replace(/"/g, '""')}"`;
          }
          return cellStr;
        }).join(',')
      ).join('\n');

      const blob = new Blob(['\uFEFF' + csvContent], {
        type: 'text/csv;charset=utf-8;'
      });
      saveAs(blob, `${filename}.csv`);
    },

    // 导出为Excel
    exportToExcel(data, filename, headers) {
      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // 设置列宽
      const colWidths = headers.map(header => ({
        wch: Math.max(header.length, 10)
      }));
      worksheet['!cols'] = colWidths;

      // 设置表头样式
      const headerRange = XLSX.utils.decode_range(worksheet['!ref']);
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!worksheet[cellAddress]) continue;

        worksheet[cellAddress].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: 'E6F3FF' } },
          alignment: { horizontal: 'center' }
        };
      }

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

      const excelBuffer = XLSX.write(workbook, {
        bookType: 'xlsx',
        type: 'array'
      });

      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      saveAs(blob, `${filename}.xlsx`);
    },

    // 处理列控制命令
    handleColumnControl(command) {
      if (command.action === 'toggle') {
        this.toggleColumn(command.column);
      }
    },

    // 处理工具栏按钮
    handleToolbarButton(button) {
      // 处理内置的导出功能
      if (button.key === 'export-csv') {
        this.exportTableData('csv', button.filename || 'table-data');
        return;
      } else if (button.key === 'export-excel') {
        this.exportTableData('xlsx', button.filename || 'table-data');
        return;
      }

      this.$emit('toolbar-button', {
        button,
        selectedRows: this.$refs.table.selection || []
      });
    },

    // 处理操作按钮
    handleAction(actionKey, row, index) {
      this.$emit('action', {
        action: actionKey,
        row,
        index
      });
    },

    // 处理操作下拉命令
    handleActionCommand(command, row, index) {
      this.$emit('action', {
        action: command.action,
        row: command.row,
        index: command.index
      });
    },

    // 分页事件处理
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;

      if (this.config.pagination && this.config.pagination.serverSide) {
        this.$emit('pagination-change', {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        });
      } else {
        this.updateTableData(this.data);
      }
    },

    handleCurrentPageChange(page) {
      this.currentPage = page;

      if (this.config.pagination && this.config.pagination.serverSide) {
        this.$emit('pagination-change', {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        });
      } else {
        this.updateTableData(this.data);
      }
    },

    // 表格事件处理
    handleSelect(selection, row) {
      this.$emit('select', selection, row);
    },

    handleSelectAll(selection) {
      this.$emit('select-all', selection);
    },

    handleSelectionChange(selection) {
      this.$emit('selection-change', selection);
    },

    handleRowClick(row, column, event) {
      this.$emit('row-click', row, column, event);
    },

    handleRowDblclick(row, column, event) {
      this.$emit('row-dblclick', row, column, event);
    },

    handleCurrentChange(currentRow, oldCurrentRow) {
      this.$emit('current-change', currentRow, oldCurrentRow);
    },

    handleSortChange({ column, prop, order }) {
      this.$emit('sort-change', { column, prop, order });
    },

    handleFilterChange(filters) {
      this.$emit('filter-change', filters);
    },

    handleExpandChange(row, expandedRows) {
      this.$emit('expand-change', row, expandedRows);
    },

    handleHeaderClick(column, event) {
      this.$emit('header-click', column, event);
    },

    handleHeaderContextmenu(column, event) {
      this.$emit('header-contextmenu', column, event);
    },

    handleRowContextmenu(row, column, event) {
      this.$emit('row-contextmenu', row, column, event);
    },

    // 公共方法
    clearSelection() {
      this.$refs.table.clearSelection();
    },

    toggleRowSelection(row, selected) {
      this.$refs.table.toggleRowSelection(row, selected);
    },

    toggleAllSelection() {
      this.$refs.table.toggleAllSelection();
    },

    toggleRowExpansion(row, expanded) {
      this.$refs.table.toggleRowExpansion(row, expanded);
    },

    setCurrentRow(row) {
      this.$refs.table.setCurrentRow(row);
    },

    clearSort() {
      this.$refs.table.clearSort();
    },

    clearFilter(columnKey) {
      this.$refs.table.clearFilter(columnKey);
    },

    doLayout() {
      this.$refs.table.doLayout();
    },

    sort(prop, order) {
      this.$refs.table.sort(prop, order);
    }
  }
};
</script>

<style scoped>
.advanced-table {
  width: 100%;
}

.table-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  /*display: flex;
  align-items: center;
  gap: 8px;*/
}

.table-pagination {
  margin-top: 16px;
  text-align: right;
}

.table-pagination .el-pagination {
  padding: 2px 5px;
}


/* 确保在小屏幕上有最小宽度 */
@media (max-width: 768px) {
  .column-setting-dialog .el-dialog {
    width: 90% !important;
  }
}

.column-setting-content {
  max-height: 400px;
}

.setting-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.setting-header .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.column-list {
  max-height: 300px;
  overflow-y: auto;
}

.column-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s;
}

.column-item:hover {
  background-color: #f9f9f9;
}

.column-item.disabled {
  opacity: 0.6;
}



.column-item .el-checkbox {
  flex: 1;
}

.column-label {
  font-size: 14px;
  color: #303133;
}

.column-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}



.setting-footer {
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid #e8e8e8;
}

.tip-text {
  font-size: 12px;
  color: #909399;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 8px;
}
</style>
