<template>
  <div class="custom-cascader-bug-fix">
    <h2>自定义Cascader BUG修复测试</h2>
    
    <div class="test-section">
      <h3>BUG修复验证</h3>
      <el-alert
        title="修复内容"
        type="info"
        :closable="false"
        show-icon
      >
        <ul>
          <li><strong>BUG1修复</strong>: Radio模式下改变选项时，下级选项值会正确跟随更新</li>
          <li><strong>BUG2修复</strong>: Checkbox模式下反选功能正常工作</li>
          <li><strong>BUG3修复</strong>: 点击行即可展开下级选项，无需点击箭头图标</li>
        </ul>
      </el-alert>
    </div>

    <div class="test-section">
      <h3>测试1: Radio模式级联更新</h3>
      <p class="test-desc">选择不同的省份，观察城市选项是否正确更新</p>
      <advanced-search
        :config="radioTestConfig"
        @search="handleRadioSearch"
        @reset="handleRadioReset"
        @field-change="handleRadioFieldChange"
      />
      <div class="result-display">
        <h4>当前选择:</h4>
        <pre>{{ JSON.stringify(radioFormData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>测试2: Checkbox模式反选测试</h3>
      <p class="test-desc">选中父级后再取消，观察子级是否正确取消选中</p>
      <advanced-search
        :config="checkboxTestConfig"
        @search="handleCheckboxSearch"
        @reset="handleCheckboxReset"
        @field-change="handleCheckboxFieldChange"
      />
      <div class="result-display">
        <h4>当前选择:</h4>
        <pre>{{ JSON.stringify(checkboxFormData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>测试3: 点击行展开测试</h3>
      <p class="test-desc">直接点击选项行，观察是否能展开下级选项</p>
      <advanced-search
        :config="clickTestConfig"
        @search="handleClickSearch"
        @reset="handleClickReset"
        @field-change="handleClickFieldChange"
      />
      <div class="result-display">
        <h4>当前选择:</h4>
        <pre>{{ JSON.stringify(clickFormData, null, 2) }}</pre>
      </div>
    </div>

    <div class="debug-section">
      <h3>实时调试信息</h3>
      <div class="debug-grid">
        <div class="debug-item">
          <h4>Radio测试数据:</h4>
          <pre>{{ JSON.stringify(radioFormData, null, 2) }}</pre>
        </div>
        <div class="debug-item">
          <h4>Checkbox测试数据:</h4>
          <pre>{{ JSON.stringify(checkboxFormData, null, 2) }}</pre>
        </div>
        <div class="debug-item">
          <h4>点击测试数据:</h4>
          <pre>{{ JSON.stringify(clickFormData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AdvancedSearch from '../components/AdvancedSearch.vue';

export default {
  name: 'CustomCascaderBugFix',
  components: {
    AdvancedSearch
  },
  data() {
    return {
      radioFormData: {},
      checkboxFormData: {},
      clickFormData: {},
      
      // Radio模式测试配置
      radioTestConfig: {
        fields: [
          {
            key: 'radioTest',
            type: 'cascader',
            label: 'Radio级联测试',
            placeholder: '请选择地区',
            span: 24,
            useCustomCascader: true,
            multiple: false,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'china',
                  label: '中国',
                  children: [
                    {
                      value: 'guangdong',
                      label: '广东省',
                      children: [
                        { value: 'guangzhou', label: '广州市' },
                        { value: 'shenzhen', label: '深圳市' },
                        { value: 'dongguan', label: '东莞市' }
                      ]
                    },
                    {
                      value: 'beijing',
                      label: '北京市',
                      children: [
                        { value: 'dongcheng', label: '东城区' },
                        { value: 'xicheng', label: '西城区' },
                        { value: 'chaoyang', label: '朝阳区' }
                      ]
                    },
                    {
                      value: 'shanghai',
                      label: '上海市',
                      children: [
                        { value: 'huangpu', label: '黄浦区' },
                        { value: 'xuhui', label: '徐汇区' },
                        { value: 'changning', label: '长宁区' }
                      ]
                    }
                  ]
                }
              ]
            }
          }
        ],
        buttons: {
          search: 'Radio测试',
          reset: '重置'
        }
      },
      
      // Checkbox模式测试配置
      checkboxTestConfig: {
        fields: [
          {
            key: 'checkboxTest',
            type: 'cascader',
            label: 'Checkbox反选测试',
            placeholder: '请选择部门',
            span: 24,
            useCustomCascader: true,
            multiple: true,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'company',
                  label: '公司总部',
                  children: [
                    {
                      value: 'tech',
                      label: '技术部',
                      children: [
                        { value: 'frontend', label: '前端组' },
                        { value: 'backend', label: '后端组' },
                        { value: 'mobile', label: '移动端组' },
                        { value: 'devops', label: '运维组' }
                      ]
                    },
                    {
                      value: 'product',
                      label: '产品部',
                      children: [
                        { value: 'pm', label: '产品经理' },
                        { value: 'ui', label: 'UI设计师' },
                        { value: 'ux', label: 'UX设计师' }
                      ]
                    },
                    {
                      value: 'sales',
                      label: '销售部',
                      children: [
                        { value: 'inside', label: '内销组' },
                        { value: 'outside', label: '外销组' },
                        { value: 'support', label: '销售支持' }
                      ]
                    }
                  ]
                }
              ]
            }
          }
        ],
        buttons: {
          search: 'Checkbox测试',
          reset: '重置'
        }
      },
      
      // 点击展开测试配置
      clickTestConfig: {
        fields: [
          {
            key: 'clickTest',
            type: 'cascader',
            label: '点击行展开测试',
            placeholder: '请选择分类',
            span: 24,
            useCustomCascader: true,
            multiple: true,
            dataSource: {
              type: 'static',
              data: [
                {
                  value: 'electronics',
                  label: '电子产品',
                  children: [
                    {
                      value: 'phones',
                      label: '手机通讯',
                      children: [
                        { value: 'smartphone', label: '智能手机' },
                        { value: 'feature_phone', label: '功能手机' },
                        { value: 'accessories', label: '手机配件' }
                      ]
                    },
                    {
                      value: 'computers',
                      label: '电脑办公',
                      children: [
                        { value: 'laptop', label: '笔记本电脑' },
                        { value: 'desktop', label: '台式电脑' },
                        { value: 'tablet', label: '平板电脑' }
                      ]
                    }
                  ]
                },
                {
                  value: 'clothing',
                  label: '服装鞋帽',
                  children: [
                    {
                      value: 'mens',
                      label: '男装',
                      children: [
                        { value: 'shirts', label: '衬衫' },
                        { value: 'pants', label: '裤子' },
                        { value: 'suits', label: '西装' }
                      ]
                    },
                    {
                      value: 'womens',
                      label: '女装',
                      children: [
                        { value: 'dresses', label: '连衣裙' },
                        { value: 'skirts', label: '裙子' },
                        { value: 'blouses', label: '上衣' }
                      ]
                    }
                  ]
                }
              ]
            }
          }
        ],
        buttons: {
          search: '点击测试',
          reset: '重置'
        }
      }
    };
  },
  
  methods: {
    // Radio测试方法
    handleRadioSearch(formData) {
      console.log('Radio测试搜索:', formData);
    },
    
    handleRadioReset() {
      console.log('Radio测试重置');
      this.radioFormData = {};
    },
    
    handleRadioFieldChange({ field, value, type }) {
      console.log(`Radio字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.radioFormData, field, value);
    },
    
    // Checkbox测试方法
    handleCheckboxSearch(formData) {
      console.log('Checkbox测试搜索:', formData);
    },
    
    handleCheckboxReset() {
      console.log('Checkbox测试重置');
      this.checkboxFormData = {};
    },
    
    handleCheckboxFieldChange({ field, value, type }) {
      console.log(`Checkbox字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.checkboxFormData, field, value);
    },
    
    // 点击测试方法
    handleClickSearch(formData) {
      console.log('点击测试搜索:', formData);
    },
    
    handleClickReset() {
      console.log('点击测试重置');
      this.clickFormData = {};
    },
    
    handleClickFieldChange({ field, value, type }) {
      console.log(`点击字段变化 - ${field}:`, value, `类型: ${type}`);
      this.$set(this.clickFormData, field, value);
    }
  }
};
</script>

<style scoped>
.custom-cascader-bug-fix {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.test-desc {
  margin-bottom: 15px;
  color: #666;
  font-style: italic;
}

.result-display {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
}

.result-display h4 {
  margin: 0 0 10px 0;
  color: #0066cc;
  font-size: 14px;
}

.result-display pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  max-height: 120px;
  overflow-y: auto;
}

.debug-section {
  margin-top: 30px;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.debug-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #856404;
}

.debug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.debug-item h4 {
  margin: 0 0 8px 0;
  color: #856404;
  font-size: 14px;
}

.debug-item pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
  overflow-x: auto;
  font-size: 11px;
  line-height: 1.3;
  margin: 0;
  max-height: 100px;
  overflow-y: auto;
}
</style>
