# Cascader多选问题修复说明

## 问题描述

在AdvancedSearch组件中集成Cascader级联选择器时，多选功能无法正常工作。

## 问题原因

### 1. 属性配置错误
原来的实现中，将`multiple`属性错误地配置在`props`对象中：

```javascript
// 错误的实现
getCascaderProps(field) {
  const props = { /* ... */ };
  
  if (field.multiple) {
    props.multiple = true;  // ❌ 错误：multiple不应该在props中
  }
  
  return props;
}
```

### 2. Element UI Cascader的正确用法
根据Element UI文档，`multiple`属性应该直接设置在`<el-cascader>`组件上，而不是在`props`配置对象中。

## 修复方案

### 1. 模板修复
在模板中直接绑定`multiple`属性：

```vue
<!-- 修复后的模板 -->
<el-cascader
  v-model="formData[field.key]"
  :options="getCascaderOptions(field)"
  :props="getCascaderProps(field)"
  :multiple="field.multiple"  <!-- ✅ 正确：直接在组件上设置 -->
  :placeholder="field.placeholder || '请选择'"
  :clearable="field.clearable !== false"
  :filterable="field.filterable"
  :show-all-levels="field.showAllLevels !== false"
  :collapse-tags="field.collapseTags"
  :separator="field.separator || ' / '"
  style="width: 100%"
  @change="handleCascaderChange(field, $event)"
/>
```

### 2. Props配置修复
移除`getCascaderProps`方法中的`multiple`设置：

```javascript
// 修复后的方法
getCascaderProps(field) {
  const defaultProps = {
    value: 'value',
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    leaf: 'leaf'
  };

  const userProps = field.cascaderProps || {};
  const props = { ...defaultProps, ...userProps };

  // 设置checkStrictly和emitPath配置
  if (field.checkStrictly !== undefined) {
    props.checkStrictly = field.checkStrictly;
  }
  
  if (field.emitPath !== undefined) {
    props.emitPath = field.emitPath;
  } else {
    // 根据模式设置默认值
    if (field.multiple) {
      props.emitPath = true;  // 多选默认返回路径
    } else {
      props.emitPath = false; // 单选默认只返回最后一级的值
    }
  }

  // 懒加载配置
  if (field.lazy) {
    props.lazy = true;
    props.lazyLoad = field.lazyLoad || this.defaultLazyLoad;
  }

  return props;
}
```

## 修复验证

### 1. 测试配置
创建了专门的测试页面 `CascaderMultipleTest.vue` 来验证多选功能：

```javascript
// 多选基础测试
{
  key: 'multiBasic',
  type: 'cascader',
  label: '多选基础',
  multiple: true,
  checkStrictly: false,
  emitPath: true,
  collapseTags: true,
  dataSource: {
    type: 'static',
    data: [/* 测试数据 */]
  }
}

// 多选严格模式测试
{
  key: 'multiStrict',
  type: 'cascader',
  label: '多选严格模式',
  multiple: true,
  checkStrictly: true,
  emitPath: false,
  collapseTags: true,
  dataSource: {
    type: 'static',
    data: [/* 测试数据 */]
  }
}
```

### 2. 实时监控
添加了实时数据监控功能，可以观察：
- 表单数据变化
- 字段变化事件
- 搜索结果

## 配置说明

### 多选相关属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `multiple` | Boolean | false | 是否多选 |
| `checkStrictly` | Boolean | false | 是否严格的遵守父子节点不互相关联 |
| `emitPath` | Boolean | true(多选)/false(单选) | 是否返回完整路径 |
| `collapseTags` | Boolean | false | 多选时是否折叠Tag |

### 配置示例

#### 1. 基础多选
```javascript
{
  key: 'departments',
  type: 'cascader',
  label: '部门',
  multiple: true,        // 启用多选
  checkStrictly: false,  // 父子关联
  emitPath: true,        // 返回完整路径
  collapseTags: true,    // 折叠标签
  dataSource: {
    type: 'static',
    data: [/* 级联数据 */]
  }
}
```

#### 2. 严格多选
```javascript
{
  key: 'skills',
  type: 'cascader',
  label: '技能',
  multiple: true,        // 启用多选
  checkStrictly: true,   // 父子不关联
  emitPath: false,       // 只返回值
  collapseTags: true,    // 折叠标签
  dataSource: {
    type: 'static',
    data: [/* 级联数据 */]
  }
}
```

## 返回值说明

### 1. 多选 + 父子关联 (checkStrictly: false)
```javascript
// 配置
{
  multiple: true,
  checkStrictly: false,
  emitPath: true
}

// 选择结果
[
  ['tech', 'frontend'],
  ['tech', 'backend']
]
```

### 2. 多选 + 父子不关联 (checkStrictly: true)
```javascript
// 配置
{
  multiple: true,
  checkStrictly: true,
  emitPath: false
}

// 选择结果
['tech', 'frontend', 'vue_team']
```

### 3. 单选对比
```javascript
// 配置
{
  multiple: false,
  emitPath: false
}

// 选择结果
'vue_team'
```

## 测试方法

### 1. 访问测试页面
1. 启动开发服务器
2. 访问应用主页
3. 切换到"Cascader多选测试"标签

### 2. 测试步骤
1. 测试多选基础功能
2. 测试多选严格模式
3. 对比单选功能
4. 观察实时数据变化
5. 验证搜索结果

### 3. 预期结果
- ✅ 多选模式正常工作
- ✅ 可以选择多个选项
- ✅ 标签正常显示和折叠
- ✅ 返回值格式正确
- ✅ 事件处理正常

## 注意事项

### 1. Element UI版本
确保使用的Element UI版本支持Cascader的多选功能（1.4+）。

### 2. 数据格式
多选时返回的数据格式会根据`emitPath`和`checkStrictly`配置而不同。

### 3. 性能考虑
大量选项时，建议启用`collapseTags`来优化显示效果。

### 4. 用户体验
- 提供清晰的占位符文本
- 合理设置默认值
- 考虑添加搜索功能

## 总结

通过将`multiple`属性从`props`配置中移到组件级别，成功修复了Cascader多选功能。现在多选模式可以正常工作，支持各种配置组合，为用户提供了灵活的级联选择体验。
